#!/usr/bin/env python3
"""Debug the tokenizer to understand the gibberish output."""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tsnn.tokenizer import TSNNTokenizer


def debug_tokenizer():
    """Debug the tokenizer."""
    print("🔍 Debugging TSNN Tokenizer")
    print("=" * 40)
    
    tokenizer = TSNNTokenizer()
    
    print(f"📊 Vocab size: {tokenizer.vocab_size}")
    print(f"🔑 Special tokens: {tokenizer.special_tokens}")
    
    # Show sample vocabulary
    print("\n📝 Sample vocabulary (first 30 tokens):")
    sample_tokens = list(tokenizer.token_to_id.items())[:30]
    for token, token_id in sample_tokens:
        print(f"  {repr(token):>15} -> {token_id}")
    
    # Test encoding/decoding
    test_texts = [
        "Hello",
        "I am a TSNN model",
        "Triton Software Labs",
        "What are you?"
    ]
    
    print("\n🧪 Testing encoding/decoding:")
    for text in test_texts:
        print(f"\n📝 Original: {repr(text)}")
        
        # Encode
        encoded = tokenizer.encode(text, add_special_tokens=False)
        input_ids = encoded["input_ids"]
        print(f"🔢 Token IDs: {input_ids.tolist()}")
        
        # Show individual tokens
        tokens = []
        for token_id in input_ids:
            token = tokenizer.id_to_token.get(token_id.item(), "<UNK>")
            tokens.append(token)
        print(f"🎯 Tokens: {tokens}")
        
        # Decode back
        decoded = tokenizer.decode(input_ids, skip_special_tokens=True)
        print(f"🔄 Decoded: {repr(decoded)}")
        
        # Check if it matches
        if decoded.strip() == text.strip():
            print("✅ Perfect match!")
        else:
            print("❌ Mismatch detected!")
    
    print(f"\n🎯 Analysis:")
    
    # Check if we have word-level tokens
    word_tokens = [token for token in tokenizer.token_to_id.keys() 
                   if len(token) > 1 and token.isalpha()]
    print(f"📚 Word-level tokens: {len(word_tokens)}")
    print(f"🔤 Character-level tokens: {tokenizer.vocab_size - len(word_tokens) - len(tokenizer.special_tokens)}")
    
    if len(word_tokens) < 50:
        print("⚠️  Very few word-level tokens! This explains the gibberish.")
        print("💡 The model is generating character by character.")
    
    print(f"\n📋 Sample word tokens:")
    for token in word_tokens[:20]:
        print(f"  {repr(token)}")


if __name__ == "__main__":
    debug_tokenizer()
