#!/usr/bin/env python3
"""
Quick TSNN Test Script
Test the TSNN architecture with minimal data to verify it works.
"""

import torch
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tsnn.tsnn_model import TSNNModel
from tsnn.tokenizer import TSNNTokenizer


def test_tsnn_basic():
    """Test basic TSNN functionality."""
    print("🧪 Testing TSNN Architecture...")
    
    # Check device
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"🎮 Using device: {device}")
    
    # Initialize tokenizer
    print("📝 Initializing tokenizer...")
    tokenizer = TSNNTokenizer()
    
    # Initialize small TSNN model
    print("🧠 Initializing TSNN model...")
    model = TSNNModel(
        vocab_size=tokenizer.vocab_size,
        d_model=128,  # Very small for testing
        n_heads=4,
        num_layers=2,
        max_seq_len=64,
        dropout=0.1
    ).to(device)
    
    print(f"📊 Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Test tokenization
    print("🔤 Testing tokenization...")
    test_text = "Hello! I am a TSNN model from Triton Software Labs."
    encoding = tokenizer.encode(test_text, max_length=32, padding=True)
    print(f"✅ Tokenization successful: {encoding['input_ids'].shape}")
    
    # Test forward pass
    print("⚡ Testing forward pass...")
    input_ids = encoding["input_ids"].unsqueeze(0).to(device)
    attention_mask = encoding["attention_mask"].unsqueeze(0).to(device)
    
    try:
        with torch.no_grad():
            outputs = model(input_ids, attention_mask)
            print(f"✅ Forward pass successful: {outputs['logits'].shape}")
            
        # Test generation
        print("🎯 Testing generation...")
        generated = model.generate(
            input_ids=input_ids[:, :5],  # Use first 5 tokens
            max_length=10,
            temperature=0.8,
            do_sample=True
        )
        print(f"✅ Generation successful: {generated.shape}")
        
        # Decode generated text
        generated_text = tokenizer.decode(generated[0], skip_special_tokens=True)
        print(f"📝 Generated text: {generated_text}")
        
        print("\n🎉 TSNN Architecture Test PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ TSNN test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_spike_patterns():
    """Test spike pattern generation."""
    print("\n🔥 Testing Spike Patterns...")
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    tokenizer = TSNNTokenizer()
    
    model = TSNNModel(
        vocab_size=tokenizer.vocab_size,
        d_model=64,
        n_heads=2,
        num_layers=1,
        max_seq_len=32
    ).to(device)
    
    test_text = "TSNN spike test"
    encoding = tokenizer.encode(test_text, max_length=16, padding=True)
    input_ids = encoding["input_ids"].unsqueeze(0).to(device)
    attention_mask = encoding["attention_mask"].unsqueeze(0).to(device)
    
    try:
        with torch.no_grad():
            outputs = model(input_ids, attention_mask, return_spikes=True)
            
            if "embedding_spikes" in outputs:
                spikes = outputs["embedding_spikes"]
                spike_rate = torch.mean(spikes.float()).item()
                print(f"✅ Spike rate: {spike_rate:.3f} ({spike_rate*100:.1f}% active)")
                
                if spike_rate < 0.5:  # Should be sparse
                    print("✅ Spikes are appropriately sparse!")
                else:
                    print("⚠️  Spikes might be too dense")
            
        print("🔥 Spike pattern test PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Spike test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🧠 TSNN Architecture Validation")
    print("=" * 50)
    
    # Test basic functionality
    basic_ok = test_tsnn_basic()
    
    if basic_ok:
        # Test spike patterns
        spike_ok = test_spike_patterns()
        
        if spike_ok:
            print("\n🎉 ALL TESTS PASSED!")
            print("✅ TSNN architecture is working correctly")
            print("🚀 Ready for training!")
            return True
    
    print("\n❌ TESTS FAILED!")
    print("🔧 Please check the error messages above")
    return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
