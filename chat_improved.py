#!/usr/bin/env python3
"""
Chat with the Improved TSNN Model
Proprietary Triton Software Labs
"""

import torch
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tsnn.simple_tsnn import SimplifiedTSNNModel
from tsnn.tokenizer import TSNNTokenizer


def load_improved_model():
    """Load the improved TSNN model."""
    model_path = "models/tsnn_improved_final.pt"
    
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        print("🔄 Please run: python train_improved.py")
        return None, None
    
    print("🔄 Loading improved TSNN model...")
    
    # Load checkpoint
    checkpoint = torch.load(model_path, map_location='cpu')
    config = checkpoint.get('config', {})
    
    print(f"📊 Model config: {config.get('d_model', 'unknown')}d, {config.get('n_heads', 'unknown')}h, {config.get('num_layers', 'unknown')}l")
    
    # Initialize tokenizer and model
    tokenizer = TSNNTokenizer()
    
    model = SimplifiedTSNNModel(
        vocab_size=config.get('vocab_size', tokenizer.vocab_size),
        d_model=config.get('d_model', 384),
        n_heads=config.get('n_heads', 6),
        num_layers=config.get('num_layers', 6),
        d_ff=config.get('d_ff', 1536),
        max_seq_len=config.get('max_seq_len', 256),
        dropout=config.get('dropout', 0.1)
    )
    
    # Load weights
    try:
        model.load_state_dict(checkpoint['model_state_dict'])
        print("✅ Improved TSNN model loaded successfully!")
        print(f"🧠 Parameters: {sum(p.numel() for p in model.parameters()):,}")
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None, None
    
    model.eval()
    
    # Move to GPU if available
    device = "cuda" if torch.cuda.is_available() else "cpu"
    model = model.to(device)
    print(f"🎮 Device: {device}")
    
    return model, tokenizer


def generate_response(model, tokenizer, user_input: str, max_length: int = 50):
    """Generate response using the improved TSNN model."""
    
    device = next(model.parameters()).device
    
    # Create conversation context
    context = f"User: {user_input}\nAI:"
    
    try:
        # Tokenize
        encoding = tokenizer.encode(context, max_length=150, padding=False, truncation=True)
        input_ids = encoding["input_ids"].unsqueeze(0).to(device)
        
        # Generate
        with torch.no_grad():
            generated = model.generate(
                input_ids=input_ids,
                max_length=input_ids.shape[1] + max_length,
                temperature=0.7,
                do_sample=True,
                top_k=40,
                top_p=0.9,
                pad_token_id=tokenizer.pad_token_id
            )
        
        # Extract generated part
        generated_part = generated[:, input_ids.shape[1]:]
        
        # Decode
        response = tokenizer.decode(generated_part[0], skip_special_tokens=True)
        
        # Clean response
        if "User:" in response:
            response = response.split("User:")[0].strip()
        if "AI:" in response:
            response = response.split("AI:")[-1].strip()
        
        # Remove extra whitespace
        response = " ".join(response.split())
        
        return response if response else "I'm still learning to respond properly with my TSNN architecture!"
        
    except Exception as e:
        print(f"❌ Generation error: {e}")
        return "I apologize, but I encountered an error while processing your message."


def main():
    """Main chat interface."""
    print("🧠 Improved TSNN Chat Interface")
    print("Proprietary Triton Software Labs")
    print("=" * 60)
    
    # Load model
    model, tokenizer = load_improved_model()
    if model is None:
        return
    
    print("\n✅ Your improved TSNN AI is ready to chat!")
    print("🎯 This model should now generate coherent responses!")
    print("Type 'quit' to exit, 'help' for commands\n")
    
    # Chat loop
    while True:
        try:
            user_input = input("You: ").strip()
            
            if not user_input:
                continue
                
            if user_input.lower() in ['quit', 'exit']:
                print("👋 Goodbye! Thanks for testing the improved TSNN architecture!")
                break
                
            if user_input.lower() == 'help':
                print("Commands:")
                print("  quit/exit - Exit the chat")
                print("  help - Show this help")
                print("  info - Show model information")
                print("  test - Run test questions")
                continue
                
            if user_input.lower() == 'info':
                info = model.get_model_info()
                print(f"🤖 Model: {info['name']}")
                print(f"🏢 Company: {info['company']}")
                print(f"🧠 Architecture: {info['architecture']}")
                print(f"⚡ Neuromorphic: {info['neuromorphic']}")
                print(f"🔥 Spike-based: {info['spike_based']}")
                continue
                
            if user_input.lower() == 'test':
                test_questions = [
                    "What are you?",
                    "Who created you?",
                    "How do you work?",
                    "Tell me about TSNN",
                    "Are you energy efficient?"
                ]
                
                print("🧪 Running test questions...")
                for question in test_questions:
                    print(f"\n👤 Test: {question}")
                    response = generate_response(model, tokenizer, question)
                    print(f"🤖 AI: {response}")
                continue
            
            # Generate response
            print("🧠 AI is thinking...", end="", flush=True)
            response = generate_response(model, tokenizer, user_input)
            print(f"\r🤖 AI: {response}\n")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye! Thanks for testing the improved TSNN architecture!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            continue


if __name__ == "__main__":
    main()
