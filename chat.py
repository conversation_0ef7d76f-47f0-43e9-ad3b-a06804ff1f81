#!/usr/bin/env python3
"""
Interactive Chat Interface for TSNN AI Chatbot
Proprietary Triton Software Labs

Command-line interface for chatting with the TSNN-powered AI assistant.
"""

import argparse
import sys
from pathlib import Path
import json
from typing import Optional

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from chatbot.ai_chatbot import AIChatbot
from chatbot.config import get_config, CONFIGS


def print_banner():
    """Print welcome banner."""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║                    🧠 AI - TSNN Chatbot 🧠                                  ║
║                                                                              ║
║                    Powered by Triton Software Labs                          ║
║                    Transformer Spiking Neural Network                       ║
║                                                                              ║
║    🔥 Neuromorphic Processing  ⚡ Energy Efficient  🧬 Biologically Inspired ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝

Welcome to AI, your neuromorphic assistant! I use spike-based computation
for energy-efficient and brain-like processing.

Type 'help' for commands, 'quit' to exit.
"""
    print(banner)


def print_help():
    """Print help information."""
    help_text = """
Available Commands:
  help              - Show this help message
  quit, exit        - Exit the chat
  clear             - Clear conversation history
  stats             - Show performance statistics
  info              - Show model information
  explain           - Explain TSNN architecture
  personality <name> - Change personality (helpful_assistant, technical_expert, creative_assistant)
  save <filename>   - Save conversation to file
  load <filename>   - Load conversation from file
  config            - Show current configuration
  
Just type your message to chat with AI!
"""
    print(help_text)


def print_stats(chatbot: AIChatbot):
    """Print performance statistics."""
    stats = chatbot.get_performance_stats()
    
    print("\n📊 Performance Statistics:")
    print(f"  Total inference time: {stats['total_inference_time']:.2f}s")
    print(f"  Total tokens generated: {stats['total_tokens_generated']}")
    print(f"  Average time per token: {stats['avg_time_per_token']:.4f}s")
    
    conv_stats = stats['conversation_stats']
    print(f"\n💬 Conversation Statistics:")
    print(f"  Duration: {conv_stats['duration_seconds']:.1f}s")
    print(f"  Total messages: {conv_stats['total_messages']}")
    print(f"  Turn count: {conv_stats['turn_count']}")
    
    if conv_stats['total_energy_cost'] > 0:
        print(f"  Total energy cost: {conv_stats['total_energy_cost']:.4f}")
        print(f"  Average energy per turn: {conv_stats['avg_energy_per_turn']:.4f}")


def print_model_info(chatbot: AIChatbot):
    """Print model information."""
    info = chatbot.get_model_info()
    
    print(f"\n🤖 Model Information:")
    print(f"  Name: {info['name']}")
    print(f"  Architecture: {info['architecture']}")
    print(f"  Company: {info['company']}")
    print(f"  Version: {info['version']}")
    print(f"  Parameters: {info['parameters']:,}")
    print(f"  Device: {info['device']}")
    print(f"  Neuromorphic: {info['neuromorphic']}")
    print(f"  Spike-based: {info['spike_based']}")


def main():
    """Main chat interface."""
    parser = argparse.ArgumentParser(description="TSNN AI Chatbot")
    parser.add_argument(
        "--config", 
        default="medium",
        choices=list(CONFIGS.keys()),
        help="Configuration preset to use"
    )
    parser.add_argument(
        "--model-path",
        type=str,
        help="Path to pre-trained model weights"
    )
    parser.add_argument(
        "--device",
        type=str,
        choices=["cpu", "cuda", "auto"],
        default="auto",
        help="Device to run model on"
    )
    parser.add_argument(
        "--personality",
        default="helpful_assistant",
        choices=["helpful_assistant", "technical_expert", "creative_assistant"],
        help="Initial personality"
    )
    parser.add_argument(
        "--no-banner",
        action="store_true",
        help="Skip welcome banner"
    )
    
    args = parser.parse_args()
    
    # Print banner
    if not args.no_banner:
        print_banner()
    
    try:
        # Initialize chatbot
        print("🔄 Initializing TSNN model...")
        config = get_config(args.config)
        config.personality = args.personality
        
        device = None if args.device == "auto" else args.device
        
        chatbot = AIChatbot(
            config=config,
            model_path=args.model_path,
            device=device
        )
        
        print("✅ AI is ready to chat!\n")
        
        # Main chat loop
        while True:
            try:
                # Get user input
                user_input = input("You: ").strip()
                
                if not user_input:
                    continue
                
                # Handle commands
                if user_input.lower() in ['quit', 'exit']:
                    print("\n👋 Goodbye! Thanks for chatting with AI!")
                    break
                    
                elif user_input.lower() == 'help':
                    print_help()
                    continue
                    
                elif user_input.lower() == 'clear':
                    chatbot.reset_conversation()
                    print("🗑️  Conversation cleared!")
                    continue
                    
                elif user_input.lower() == 'stats':
                    print_stats(chatbot)
                    continue
                    
                elif user_input.lower() == 'info':
                    print_model_info(chatbot)
                    continue
                    
                elif user_input.lower() == 'explain':
                    explanation = chatbot.explain_tsnn()
                    print(f"\nAI: {explanation}")
                    continue
                    
                elif user_input.lower() == 'config':
                    config_dict = chatbot.config.to_dict()
                    print("\n⚙️  Current Configuration:")
                    for key, value in config_dict.items():
                        print(f"  {key}: {value}")
                    continue
                    
                elif user_input.lower().startswith('personality '):
                    personality = user_input.split(' ', 1)[1]
                    try:
                        chatbot.set_personality(personality)
                        print(f"🎭 Personality changed to: {personality}")
                    except ValueError as e:
                        print(f"❌ {e}")
                    continue
                    
                elif user_input.lower().startswith('save '):
                    filename = user_input.split(' ', 1)[1]
                    try:
                        chatbot.save_conversation(filename)
                        print(f"💾 Conversation saved to: {filename}")
                    except Exception as e:
                        print(f"❌ Failed to save: {e}")
                    continue
                    
                elif user_input.lower().startswith('load '):
                    filename = user_input.split(' ', 1)[1]
                    try:
                        chatbot.load_conversation(filename)
                        print(f"📂 Conversation loaded from: {filename}")
                    except Exception as e:
                        print(f"❌ Failed to load: {e}")
                    continue
                
                # Generate response
                print("🧠 AI is thinking...", end="", flush=True)
                response = chatbot.chat(user_input)
                print(f"\r🤖 AI: {response}\n")
                
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye! Thanks for chatting with AI!")
                break
                
            except Exception as e:
                print(f"\n❌ An error occurred: {e}")
                print("Please try again or type 'help' for commands.\n")
                
    except Exception as e:
        print(f"❌ Failed to initialize chatbot: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
