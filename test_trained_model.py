#!/usr/bin/env python3
"""
Test the trained TSNN model
"""

import torch
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tsnn.simple_tsnn import SimplifiedTSNNModel
from tsnn.tokenizer import TSNNTokenizer


def test_model():
    """Test the trained model."""
    print("🧠 Testing Trained TSNN Model")
    print("=" * 40)
    
    # Load model
    model_path = "models/tsnn_chatbot_final.pt"
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        return False
    
    print("📁 Loading model...")
    checkpoint = torch.load(model_path, map_location='cpu')
    config = checkpoint.get('config', {})
    
    print("⚙️ Model configuration:")
    print(f"  Vocab size: {config.get('vocab_size', 'unknown')}")
    print(f"  Model dim: {config.get('d_model', 'unknown')}")
    print(f"  Heads: {config.get('n_heads', 'unknown')}")
    print(f"  Layers: {config.get('num_layers', 'unknown')}")
    
    # Initialize tokenizer and model
    tokenizer = TSNNTokenizer()
    
    model = SimplifiedTSNNModel(
        vocab_size=config.get('vocab_size', tokenizer.vocab_size),
        d_model=config.get('d_model', 256),
        n_heads=config.get('n_heads', 4),
        num_layers=config.get('num_layers', 4),
        d_ff=config.get('d_ff', 1024),
        max_seq_len=config.get('max_seq_len', 256),
        dropout=config.get('dropout', 0.1)
    )
    
    # Load weights
    try:
        model.load_state_dict(checkpoint['model_state_dict'])
        print("✅ Model weights loaded successfully!")
    except Exception as e:
        print(f"❌ Error loading weights: {e}")
        return False
    
    model.eval()
    
    # Test generation
    print("\n🎯 Testing text generation...")
    test_inputs = [
        "Hello",
        "What are you?",
        "How do you work?",
        "Tell me about TSNN"
    ]
    
    for test_input in test_inputs:
        print(f"\n👤 User: {test_input}")
        
        try:
            # Prepare input
            context = f"User: {test_input}\nAI:"
            encoding = tokenizer.encode(context, max_length=50, padding=False, truncation=True)
            input_ids = encoding["input_ids"].unsqueeze(0)
            
            # Generate
            with torch.no_grad():
                generated = model.generate(
                    input_ids=input_ids,
                    max_length=input_ids.shape[1] + 30,
                    temperature=0.8,
                    do_sample=True,
                    top_k=50
                )
            
            # Decode response
            generated_part = generated[:, input_ids.shape[1]:]
            response = tokenizer.decode(generated_part[0], skip_special_tokens=True)
            
            # Clean response
            if "User:" in response:
                response = response.split("User:")[0].strip()
            if "AI:" in response:
                response = response.split("AI:")[-1].strip()
            
            print(f"🤖 AI: {response}")
            
        except Exception as e:
            print(f"❌ Generation failed: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n🎉 TSNN Model Test Complete!")
    return True


if __name__ == "__main__":
    success = test_model()
    if success:
        print("\n✅ Your TSNN architecture is working!")
        print("🚀 The model can generate responses!")
        print("💬 Try: python simple_chat.py")
    else:
        print("\n❌ Model test failed")
