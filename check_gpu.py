#!/usr/bin/env python3
"""
GPU Memory and CUDA Check for TSNN Training
Proprietary Triton Software Labs

Check GPU capabilities and recommend optimal training settings.
"""

import torch
import logging
from pathlib import Path


def check_cuda_setup():
    """Check CUDA installation and GPU capabilities."""
    print("🔍 CUDA and GPU Check")
    print("=" * 50)
    
    # Check CUDA availability
    if not torch.cuda.is_available():
        print("❌ CUDA not available!")
        print("   Please install CUDA toolkit and PyTorch with CUDA support")
        print("   Visit: https://pytorch.org/get-started/locally/")
        return False
    
    print("✅ CUDA is available!")
    print(f"🔧 CUDA Version: {torch.version.cuda}")
    print(f"🐍 PyTorch Version: {torch.__version__}")
    
    # Check GPU details
    gpu_count = torch.cuda.device_count()
    print(f"🎮 GPU Count: {gpu_count}")
    
    for i in range(gpu_count):
        props = torch.cuda.get_device_properties(i)
        memory_gb = props.total_memory / (1024**3)
        
        print(f"\n📱 GPU {i}: {props.name}")
        print(f"   💾 Total Memory: {memory_gb:.1f} GB")
        print(f"   🔢 Compute Capability: {props.major}.{props.minor}")
        print(f"   🧮 Multiprocessors: {props.multi_processor_count}")
        
        # Check current memory usage
        torch.cuda.set_device(i)
        torch.cuda.empty_cache()
        
        allocated = torch.cuda.memory_allocated(i) / (1024**3)
        cached = torch.cuda.memory_reserved(i) / (1024**3)
        free = memory_gb - cached
        
        print(f"   🔄 Available Memory: {free:.1f} GB")
        print(f"   📊 Memory Usage: {allocated:.2f} GB allocated, {cached:.2f} GB cached")
        
        # Recommendations based on GPU
        print(f"\n💡 Recommendations for {props.name}:")
        
        if "1060" in props.name:
            print("   🎯 GTX 1060 Detected - Optimized Settings:")
            print("   ├─ Config: small or energy_efficient")
            print("   ├─ Batch Size: 1-2")
            print("   ├─ Max Samples: 5000-8000")
            print("   ├─ Use: python train_gtx1060.py")
            print("   └─ Memory: Monitor VRAM usage closely")
            
        elif "1070" in props.name or "1080" in props.name:
            print("   🎯 GTX 1070/1080 - Good Performance:")
            print("   ├─ Config: medium")
            print("   ├─ Batch Size: 2-4")
            print("   ├─ Max Samples: 8000-12000")
            print("   └─ Memory: Should handle medium models well")
            
        elif "RTX" in props.name or "2060" in props.name or "2070" in props.name:
            print("   🎯 RTX Series - Excellent Performance:")
            print("   ├─ Config: medium or large")
            print("   ├─ Batch Size: 4-8")
            print("   ├─ Max Samples: 10000-20000")
            print("   └─ Memory: Can handle larger models")
            
        elif memory_gb >= 8:
            print("   🎯 High-End GPU - Maximum Performance:")
            print("   ├─ Config: large or high_performance")
            print("   ├─ Batch Size: 8-16")
            print("   ├─ Max Samples: 15000+")
            print("   └─ Memory: Can handle full datasets")
            
        else:
            print("   🎯 General Recommendations:")
            print("   ├─ Config: small")
            print("   ├─ Batch Size: 1-2")
            print("   ├─ Max Samples: 3000-5000")
            print("   └─ Memory: Use conservative settings")
    
    return True


def test_memory_allocation():
    """Test GPU memory allocation with a small tensor."""
    if not torch.cuda.is_available():
        return False
    
    print("\n🧪 Memory Allocation Test")
    print("=" * 30)
    
    try:
        # Test small allocation
        device = torch.cuda.current_device()
        test_tensor = torch.randn(1000, 1000, device='cuda')
        
        allocated = torch.cuda.memory_allocated(device) / (1024**2)
        print(f"✅ Test allocation successful: {allocated:.1f} MB")
        
        # Clean up
        del test_tensor
        torch.cuda.empty_cache()
        
        print("✅ Memory cleanup successful")
        return True
        
    except RuntimeError as e:
        print(f"❌ Memory allocation failed: {e}")
        return False


def recommend_training_command():
    """Recommend optimal training command based on GPU."""
    if not torch.cuda.is_available():
        print("\n💻 CPU Training Recommendation:")
        print("python train_with_data.py --config small --device cpu --max-samples 2000")
        return
    
    props = torch.cuda.get_device_properties(0)
    memory_gb = props.total_memory / (1024**3)
    
    print(f"\n🚀 Recommended Training Commands for {props.name}:")
    print("=" * 60)
    
    if "1060" in props.name or memory_gb < 7:
        print("🎯 GTX 1060 / 6GB VRAM Optimized:")
        print("python train_gtx1060.py --config small --max-samples 6000")
        print("python train_gtx1060.py --config energy_efficient --max-samples 8000")
        print("\n🔄 Alternative (regular script):")
        print("python train_with_data.py --config small --batch-size 1 --max-samples 6000")
        
    elif memory_gb < 10:
        print("🎯 Mid-Range GPU (8GB VRAM):")
        print("python train_with_data.py --config medium --batch-size 2 --max-samples 10000")
        print("python train_with_data.py --config energy_efficient --batch-size 4 --max-samples 12000")
        
    else:
        print("🎯 High-End GPU (10GB+ VRAM):")
        print("python train_with_data.py --config large --batch-size 4 --max-samples 15000")
        print("python train_with_data.py --config high_performance --batch-size 8 --max-samples 20000")
    
    print(f"\n⚡ Quick Test (recommended first):")
    if "1060" in props.name:
        print("python train_gtx1060.py --quick-test")
    else:
        print("python train_with_data.py --config small --max-samples 1000")


def main():
    """Main function."""
    print("🧠 TSNN GPU Compatibility Check")
    print("Proprietary Triton Software Labs")
    print("=" * 80)
    
    # Check CUDA setup
    cuda_ok = check_cuda_setup()
    
    if cuda_ok:
        # Test memory allocation
        memory_ok = test_memory_allocation()
        
        if memory_ok:
            # Provide recommendations
            recommend_training_command()
            
            print(f"\n📁 Data Check:")
            data_dir = Path("data")
            if data_dir.exists():
                json_files = list(data_dir.rglob("*.json"))
                print(f"✅ Found {len(json_files)} JSON files in data/ directory")
                print("🔍 Run: python inspect_data.py --show-samples")
            else:
                print("❌ data/ directory not found")
                print("📥 Please add your training data to data/ directory")
        
        print(f"\n🎉 Ready to train your TSNN chatbot!")
        
    else:
        print(f"\n❌ CUDA setup incomplete. Please install CUDA and PyTorch with GPU support.")


if __name__ == "__main__":
    main()
