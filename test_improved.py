#!/usr/bin/env python3
"""Test the improved TSNN model"""

import torch
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tsnn.simple_tsnn import SimplifiedTSNNModel
from tsnn.tokenizer import TSNNTokenizer


def test_improved_model():
    """Test the improved TSNN model."""
    print("🧠 Testing Improved TSNN Model")
    print("=" * 50)
    
    # Load model
    model_path = "models/tsnn_improved_final.pt"
    if not os.path.exists(model_path):
        print("❌ Improved model not found")
        return
    
    print("📁 Loading improved model...")
    checkpoint = torch.load(model_path, map_location='cpu')
    config = checkpoint.get('config', {})
    
    tokenizer = TSNNTokenizer()
    model = SimplifiedTSNNModel(
        vocab_size=config.get('vocab_size', tokenizer.vocab_size),
        d_model=config.get('d_model', 384),
        n_heads=config.get('n_heads', 6),
        num_layers=config.get('num_layers', 6),
        d_ff=config.get('d_ff', 1536),
        max_seq_len=config.get('max_seq_len', 256),
        dropout=config.get('dropout', 0.1)
    )
    
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    # Move to GPU if available
    device = "cuda" if torch.cuda.is_available() else "cpu"
    model = model.to(device)
    
    print(f"✅ Model loaded: {sum(p.numel() for p in model.parameters()):,} parameters")
    print(f"🎮 Device: {device}")
    
    # Test questions
    test_questions = [
        "Hello",
        "What are you?",
        "Who created you?",
        "How do you work?",
        "Tell me about TSNN",
        "Are you energy efficient?",
        "Can you help me with coding?"
    ]
    
    print(f"\n🧪 Testing improved TSNN responses...")
    print("=" * 50)
    
    for question in test_questions:
        print(f"\n👤 User: {question}")
        
        try:
            # Create context
            context = f"User: {question}\nAI:"
            
            # Tokenize
            encoding = tokenizer.encode(context, max_length=100, padding=False, truncation=True)
            input_ids = encoding["input_ids"].unsqueeze(0).to(device)
            
            # Generate
            with torch.no_grad():
                generated = model.generate(
                    input_ids=input_ids,
                    max_length=input_ids.shape[1] + 40,
                    temperature=0.7,
                    do_sample=True,
                    top_k=40,
                    top_p=0.9
                )
            
            # Decode
            generated_part = generated[:, input_ids.shape[1]:]
            response = tokenizer.decode(generated_part[0], skip_special_tokens=True)
            
            # Clean
            if "User:" in response:
                response = response.split("User:")[0].strip()
            if "AI:" in response:
                response = response.split("AI:")[-1].strip()
            
            response = " ".join(response.split())  # Clean whitespace
            
            print(f"🤖 AI: {response}")
            
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print(f"\n🎉 Improved TSNN test complete!")
    print("💬 To chat interactively, run: python chat_improved.py")


if __name__ == "__main__":
    test_improved_model()
