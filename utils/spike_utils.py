"""
Spike Processing Utilities for TSNN
Proprietary Triton Software Labs

Utilities for analyzing and visualizing spike patterns in TSNN models.
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from typing import Tuple, List, Optional, Dict, Any
import seaborn as sns


def analyze_spike_patterns(spikes: torch.Tensor) -> Dict[str, float]:
    """
    Analyze spike patterns and compute statistics.
    
    Args:
        spikes: Spike tensor [batch_size, seq_len, features]
        
    Returns:
        stats: Dictionary of spike statistics
    """
    # Convert to numpy for analysis
    if isinstance(spikes, torch.Tensor):
        spikes_np = spikes.detach().cpu().numpy()
    else:
        spikes_np = spikes
    
    # Basic statistics
    total_spikes = np.sum(spikes_np)
    total_possible = np.prod(spikes_np.shape)
    spike_rate = total_spikes / total_possible
    
    # Temporal statistics
    spikes_per_timestep = np.sum(spikes_np, axis=(0, 2))  # Sum over batch and features
    temporal_variance = np.var(spikes_per_timestep)
    
    # Spatial statistics
    spikes_per_neuron = np.sum(spikes_np, axis=(0, 1))  # Sum over batch and time
    spatial_variance = np.var(spikes_per_neuron)
    
    # Sparsity measure
    sparsity = 1.0 - spike_rate
    
    # Burst detection (consecutive spikes)
    burst_lengths = []
    for b in range(spikes_np.shape[0]):
        for f in range(spikes_np.shape[2]):
            spike_train = spikes_np[b, :, f]
            in_burst = False
            current_burst = 0
            
            for spike in spike_train:
                if spike > 0:
                    if in_burst:
                        current_burst += 1
                    else:
                        in_burst = True
                        current_burst = 1
                else:
                    if in_burst:
                        burst_lengths.append(current_burst)
                        in_burst = False
                        current_burst = 0
            
            if in_burst:
                burst_lengths.append(current_burst)
    
    avg_burst_length = np.mean(burst_lengths) if burst_lengths else 0.0
    
    return {
        "spike_rate": float(spike_rate),
        "sparsity": float(sparsity),
        "total_spikes": int(total_spikes),
        "temporal_variance": float(temporal_variance),
        "spatial_variance": float(spatial_variance),
        "avg_burst_length": float(avg_burst_length),
        "num_bursts": len(burst_lengths)
    }


def compute_spike_correlations(spikes: torch.Tensor, 
                             max_lag: int = 10) -> torch.Tensor:
    """
    Compute cross-correlations between spike trains.
    
    Args:
        spikes: Spike tensor [batch_size, seq_len, features]
        max_lag: Maximum lag for correlation computation
        
    Returns:
        correlations: Cross-correlation matrix [features, features, 2*max_lag+1]
    """
    batch_size, seq_len, features = spikes.shape
    correlations = torch.zeros(features, features, 2 * max_lag + 1)
    
    # Convert to numpy for easier processing
    spikes_np = spikes.detach().cpu().numpy()
    
    for i in range(features):
        for j in range(features):
            # Average correlation across batch
            batch_corr = []
            
            for b in range(batch_size):
                spike_train_i = spikes_np[b, :, i]
                spike_train_j = spikes_np[b, :, j]
                
                # Compute cross-correlation
                corr = np.correlate(
                    np.pad(spike_train_i, max_lag, mode='constant'),
                    spike_train_j,
                    mode='valid'
                )
                batch_corr.append(corr)
            
            # Average across batch
            avg_corr = np.mean(batch_corr, axis=0)
            correlations[i, j, :] = torch.from_numpy(avg_corr)
    
    return correlations


def detect_spike_synchrony(spikes: torch.Tensor, 
                          window_size: int = 5) -> torch.Tensor:
    """
    Detect synchronous spiking events.
    
    Args:
        spikes: Spike tensor [batch_size, seq_len, features]
        window_size: Time window for synchrony detection
        
    Returns:
        synchrony: Synchrony measure over time [batch_size, seq_len]
    """
    batch_size, seq_len, features = spikes.shape
    synchrony = torch.zeros(batch_size, seq_len)
    
    for t in range(seq_len):
        # Define time window
        start_t = max(0, t - window_size // 2)
        end_t = min(seq_len, t + window_size // 2 + 1)
        
        # Count simultaneous spikes in window
        window_spikes = spikes[:, start_t:end_t, :]
        spike_counts = torch.sum(window_spikes, dim=2)  # Sum over features
        
        # Synchrony is the variance in spike counts (high variance = synchrony)
        synchrony[:, t] = torch.var(spike_counts, dim=1)
    
    return synchrony


def visualize_spike_raster(spikes: torch.Tensor,
                          batch_idx: int = 0,
                          max_neurons: int = 50,
                          figsize: Tuple[int, int] = (12, 8),
                          save_path: Optional[str] = None) -> None:
    """
    Create a raster plot of spike patterns.
    
    Args:
        spikes: Spike tensor [batch_size, seq_len, features]
        batch_idx: Which batch to visualize
        max_neurons: Maximum number of neurons to show
        figsize: Figure size
        save_path: Path to save figure
    """
    if isinstance(spikes, torch.Tensor):
        spikes_np = spikes[batch_idx].detach().cpu().numpy()
    else:
        spikes_np = spikes[batch_idx]
    
    seq_len, features = spikes_np.shape
    features = min(features, max_neurons)
    
    plt.figure(figsize=figsize)
    
    # Create raster plot
    for neuron in range(features):
        spike_times = np.where(spikes_np[:, neuron] > 0)[0]
        plt.scatter(spike_times, [neuron] * len(spike_times), 
                   s=2, c='black', alpha=0.7)
    
    plt.xlabel('Time Step')
    plt.ylabel('Neuron Index')
    plt.title(f'TSNN Spike Raster Plot (Batch {batch_idx})')
    plt.grid(True, alpha=0.3)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.show()


def visualize_spike_rates(spikes: torch.Tensor,
                         window_size: int = 10,
                         figsize: Tuple[int, int] = (12, 6),
                         save_path: Optional[str] = None) -> None:
    """
    Visualize spike rates over time.
    
    Args:
        spikes: Spike tensor [batch_size, seq_len, features]
        window_size: Window size for rate computation
        figsize: Figure size
        save_path: Path to save figure
    """
    # Average over batch and features
    avg_spikes = torch.mean(spikes, dim=(0, 2)).detach().cpu().numpy()
    
    # Compute sliding window rates
    seq_len = len(avg_spikes)
    rates = []
    time_points = []
    
    for t in range(window_size, seq_len - window_size):
        window_rate = np.mean(avg_spikes[t-window_size:t+window_size])
        rates.append(window_rate)
        time_points.append(t)
    
    plt.figure(figsize=figsize)
    plt.plot(time_points, rates, linewidth=2, color='blue')
    plt.fill_between(time_points, rates, alpha=0.3, color='blue')
    
    plt.xlabel('Time Step')
    plt.ylabel('Average Spike Rate')
    plt.title('TSNN Spike Rate Over Time')
    plt.grid(True, alpha=0.3)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.show()


def compute_energy_efficiency(spikes: torch.Tensor,
                            baseline_activity: float = 1.0) -> Dict[str, float]:
    """
    Compute energy efficiency metrics for spike-based processing.
    
    Args:
        spikes: Spike tensor [batch_size, seq_len, features]
        baseline_activity: Baseline activity level for comparison
        
    Returns:
        efficiency_metrics: Dictionary of efficiency metrics
    """
    # Spike statistics
    spike_stats = analyze_spike_patterns(spikes)
    spike_rate = spike_stats["spike_rate"]
    sparsity = spike_stats["sparsity"]
    
    # Energy efficiency (higher sparsity = lower energy)
    energy_efficiency = sparsity
    
    # Computational efficiency (fewer operations needed)
    computational_savings = 1.0 - spike_rate
    
    # Compare to baseline (traditional continuous processing)
    energy_ratio = spike_rate / baseline_activity
    
    return {
        "energy_efficiency": float(energy_efficiency),
        "computational_savings": float(computational_savings),
        "energy_ratio_vs_baseline": float(energy_ratio),
        "estimated_energy_reduction": float(1.0 - energy_ratio),
        "spike_rate": float(spike_rate),
        "sparsity": float(sparsity)
    }


def create_spike_heatmap(spikes: torch.Tensor,
                        batch_idx: int = 0,
                        figsize: Tuple[int, int] = (12, 8),
                        save_path: Optional[str] = None) -> None:
    """
    Create a heatmap visualization of spike patterns.
    
    Args:
        spikes: Spike tensor [batch_size, seq_len, features]
        batch_idx: Which batch to visualize
        figsize: Figure size
        save_path: Path to save figure
    """
    if isinstance(spikes, torch.Tensor):
        spikes_np = spikes[batch_idx].detach().cpu().numpy()
    else:
        spikes_np = spikes[batch_idx]
    
    plt.figure(figsize=figsize)
    
    # Create heatmap
    sns.heatmap(
        spikes_np.T,  # Transpose for better visualization
        cmap='Blues',
        cbar_kws={'label': 'Spike Activity'},
        xticklabels=False,
        yticklabels=False
    )
    
    plt.xlabel('Time Step')
    plt.ylabel('Neuron Index')
    plt.title(f'TSNN Spike Activity Heatmap (Batch {batch_idx})')
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.show()


def export_spike_data(spikes: torch.Tensor,
                     output_path: str,
                     format: str = "numpy") -> None:
    """
    Export spike data to file.
    
    Args:
        spikes: Spike tensor to export
        output_path: Output file path
        format: Export format ("numpy", "csv", "json")
    """
    spikes_np = spikes.detach().cpu().numpy()
    
    if format == "numpy":
        np.save(output_path, spikes_np)
    elif format == "csv":
        # Flatten for CSV export
        batch_size, seq_len, features = spikes_np.shape
        reshaped = spikes_np.reshape(-1, features)
        np.savetxt(output_path, reshaped, delimiter=',')
    elif format == "json":
        import json
        # Convert to list for JSON serialization
        spikes_list = spikes_np.tolist()
        with open(output_path, 'w') as f:
            json.dump(spikes_list, f)
    else:
        raise ValueError(f"Unknown format: {format}")
    
    print(f"Spike data exported to: {output_path}")


def load_spike_data(input_path: str,
                   format: str = "numpy") -> torch.Tensor:
    """
    Load spike data from file.
    
    Args:
        input_path: Input file path
        format: File format ("numpy", "csv", "json")
        
    Returns:
        spikes: Loaded spike tensor
    """
    if format == "numpy":
        spikes_np = np.load(input_path)
    elif format == "csv":
        spikes_np = np.loadtxt(input_path, delimiter=',')
    elif format == "json":
        import json
        with open(input_path, 'r') as f:
            spikes_list = json.load(f)
        spikes_np = np.array(spikes_list)
    else:
        raise ValueError(f"Unknown format: {format}")
    
    return torch.from_numpy(spikes_np).float()
