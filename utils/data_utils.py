"""
Data Processing Utilities for TSNN Chatbot
Proprietary Triton Software Labs

Utilities for data preprocessing, conversation formatting, and dataset management.
"""

import json
import csv
import re
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Union
import torch
import numpy as np
from datetime import datetime


def clean_text(text: str) -> str:
    """
    Clean and normalize text for TSNN processing.
    
    Args:
        text: Raw text string
        
    Returns:
        cleaned_text: Cleaned text string
    """
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Normalize quotes
    text = re.sub(r'["""]', '"', text)
    text = re.sub(r'[''']', "'", text)
    
    # Remove control characters
    text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
    
    # Normalize dashes
    text = re.sub(r'[–—]', '-', text)
    
    return text


def format_conversation(messages: List[Dict[str, str]], 
                       include_system: bool = True) -> str:
    """
    Format conversation messages into a single string.
    
    Args:
        messages: List of message dictionaries
        include_system: Whether to include system messages
        
    Returns:
        formatted_conversation: Formatted conversation string
    """
    formatted_parts = []
    
    for message in messages:
        role = message.get("role", "unknown")
        content = message.get("content", "")
        
        if role == "system" and not include_system:
            continue
            
        if role == "user":
            formatted_parts.append(f"User: {content}")
        elif role == "assistant":
            formatted_parts.append(f"AI: {content}")
        elif role == "system":
            formatted_parts.append(f"System: {content}")
    
    return "\n".join(formatted_parts)


def parse_conversation_file(file_path: str) -> List[Dict[str, Any]]:
    """
    Parse conversation data from various file formats.
    
    Args:
        file_path: Path to conversation file
        
    Returns:
        conversations: List of conversation dictionaries
    """
    file_path = Path(file_path)
    
    if not file_path.exists():
        raise FileNotFoundError(f"File not found: {file_path}")
    
    if file_path.suffix.lower() == '.json':
        return parse_json_conversations(file_path)
    elif file_path.suffix.lower() == '.csv':
        return parse_csv_conversations(file_path)
    elif file_path.suffix.lower() in ['.txt', '.md']:
        return parse_text_conversations(file_path)
    else:
        raise ValueError(f"Unsupported file format: {file_path.suffix}")


def parse_json_conversations(file_path: Path) -> List[Dict[str, Any]]:
    """Parse JSON conversation file."""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    conversations = []

    if isinstance(data, list):
        for item in data:
            if "messages" in item:
                # Standard format: {"messages": [{"role": "user", "content": "..."}, ...]}
                conversations.append(item)
            elif "prompt" in item and "ai_response" in item:
                # AI coding dataset format: {"prompt": "...", "ai_response": "..."}
                messages = [
                    {"role": "user", "content": item["prompt"]},
                    {"role": "assistant", "content": item["ai_response"]}
                ]
                conversations.append({"messages": messages})
            elif "input" in item and "output" in item:
                # Input-output format: {"input": "...", "output": "..."}
                messages = [
                    {"role": "user", "content": item["input"]},
                    {"role": "assistant", "content": item["output"]}
                ]
                conversations.append({"messages": messages})
            elif "question" in item and "answer" in item:
                # Q&A format: {"question": "...", "answer": "..."}
                messages = [
                    {"role": "user", "content": item["question"]},
                    {"role": "assistant", "content": item["answer"]}
                ]
                conversations.append({"messages": messages})
        return conversations
    elif isinstance(data, dict):
        if "conversations" in data:
            return data["conversations"]
        elif "messages" in data:
            return [data]
        elif "prompt" in data and "ai_response" in data:
            messages = [
                {"role": "user", "content": data["prompt"]},
                {"role": "assistant", "content": data["ai_response"]}
            ]
            return [{"messages": messages}]
        else:
            return [data]
    else:
        raise ValueError("Invalid JSON format for conversations")


def parse_csv_conversations(file_path: Path) -> List[Dict[str, Any]]:
    """Parse CSV conversation file."""
    conversations = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        current_conv = []
        current_conv_id = None
        
        for row in reader:
            conv_id = row.get("conversation_id", "default")
            role = row.get("role", "user")
            content = row.get("content", "")
            
            if conv_id != current_conv_id:
                if current_conv:
                    conversations.append({"messages": current_conv})
                current_conv = []
                current_conv_id = conv_id
            
            current_conv.append({
                "role": role,
                "content": clean_text(content)
            })
        
        if current_conv:
            conversations.append({"messages": current_conv})
    
    return conversations


def parse_text_conversations(file_path: Path) -> List[Dict[str, Any]]:
    """Parse text conversation file."""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    conversations = []
    
    # Split by conversation separators
    conv_sections = re.split(r'\n\s*---\s*\n|\n\s*===\s*\n', content)
    
    for section in conv_sections:
        if not section.strip():
            continue
            
        messages = []
        lines = section.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Parse different formats
            if line.startswith("User: ") or line.startswith("Human: "):
                content = line.split(": ", 1)[1] if ": " in line else line
                messages.append({"role": "user", "content": clean_text(content)})
            elif line.startswith("AI: ") or line.startswith("Assistant: ") or line.startswith("Bot: "):
                content = line.split(": ", 1)[1] if ": " in line else line
                messages.append({"role": "assistant", "content": clean_text(content)})
            elif line.startswith("System: "):
                content = line.split(": ", 1)[1] if ": " in line else line
                messages.append({"role": "system", "content": clean_text(content)})
        
        if messages:
            conversations.append({"messages": messages})
    
    return conversations


def create_training_pairs(conversations: List[Dict[str, Any]],
                         max_context_length: int = 3) -> List[Dict[str, str]]:
    """
    Create input-output pairs for training.
    
    Args:
        conversations: List of conversation dictionaries
        max_context_length: Maximum number of previous messages to include
        
    Returns:
        training_pairs: List of input-output pairs
    """
    training_pairs = []
    
    for conv in conversations:
        messages = conv.get("messages", [])
        
        for i, message in enumerate(messages):
            if message["role"] != "assistant":
                continue
                
            # Build context from previous messages
            context_messages = []
            start_idx = max(0, i - max_context_length)
            
            for j in range(start_idx, i):
                prev_message = messages[j]
                if prev_message["role"] in ["user", "assistant"]:
                    context_messages.append(prev_message)
            
            # Add current user message if it exists
            if i > 0 and messages[i-1]["role"] == "user":
                context_messages.append(messages[i-1])
            
            # Create input-output pair
            if context_messages:
                input_text = format_conversation(context_messages, include_system=False)
                output_text = message["content"]
                
                training_pairs.append({
                    "input": input_text,
                    "output": output_text
                })
    
    return training_pairs


def augment_training_data(training_pairs: List[Dict[str, str]],
                         augmentation_factor: float = 0.1) -> List[Dict[str, str]]:
    """
    Augment training data with variations.
    
    Args:
        training_pairs: Original training pairs
        augmentation_factor: Fraction of data to augment
        
    Returns:
        augmented_pairs: Augmented training pairs
    """
    augmented_pairs = training_pairs.copy()
    num_to_augment = int(len(training_pairs) * augmentation_factor)
    
    # Simple augmentations
    augmentations = [
        lambda x: x.replace(".", "!"),  # Change punctuation
        lambda x: x.replace("?", "."),
        lambda x: x.replace(" I ", " I "),  # Identity (no change)
        lambda x: x.replace("you", "you"),  # Identity
    ]
    
    for i in range(num_to_augment):
        original_idx = np.random.randint(0, len(training_pairs))
        original_pair = training_pairs[original_idx]
        
        # Apply random augmentation
        aug_func = np.random.choice(augmentations)
        augmented_input = aug_func(original_pair["input"])
        
        augmented_pairs.append({
            "input": augmented_input,
            "output": original_pair["output"]
        })
    
    return augmented_pairs


def split_dataset(data: List[Dict[str, str]], 
                 train_ratio: float = 0.8,
                 val_ratio: float = 0.1,
                 test_ratio: float = 0.1,
                 random_seed: int = 42) -> Tuple[List, List, List]:
    """
    Split dataset into train/validation/test sets.
    
    Args:
        data: List of data samples
        train_ratio: Fraction for training
        val_ratio: Fraction for validation
        test_ratio: Fraction for testing
        random_seed: Random seed for reproducibility
        
    Returns:
        train_data, val_data, test_data: Split datasets
    """
    assert abs(train_ratio + val_ratio + test_ratio - 1.0) < 1e-6, \
        "Ratios must sum to 1.0"
    
    np.random.seed(random_seed)
    indices = np.random.permutation(len(data))
    
    train_end = int(len(data) * train_ratio)
    val_end = train_end + int(len(data) * val_ratio)
    
    train_indices = indices[:train_end]
    val_indices = indices[train_end:val_end]
    test_indices = indices[val_end:]
    
    train_data = [data[i] for i in train_indices]
    val_data = [data[i] for i in val_indices]
    test_data = [data[i] for i in test_indices]
    
    return train_data, val_data, test_data


def save_dataset(data: List[Dict[str, str]], 
                output_path: str,
                format: str = "json") -> None:
    """
    Save dataset to file.
    
    Args:
        data: Dataset to save
        output_path: Output file path
        format: Output format ("json", "csv")
    """
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    if format == "json":
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    elif format == "csv":
        with open(output_path, 'w', newline='', encoding='utf-8') as f:
            if data:
                fieldnames = data[0].keys()
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(data)
    else:
        raise ValueError(f"Unsupported format: {format}")


def load_dataset(input_path: str) -> List[Dict[str, str]]:
    """
    Load dataset from file.
    
    Args:
        input_path: Input file path
        
    Returns:
        data: Loaded dataset
    """
    input_path = Path(input_path)
    
    if input_path.suffix.lower() == '.json':
        with open(input_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    elif input_path.suffix.lower() == '.csv':
        data = []
        with open(input_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                data.append(dict(row))
        return data
    else:
        raise ValueError(f"Unsupported file format: {input_path.suffix}")


def compute_dataset_statistics(data: List[Dict[str, str]]) -> Dict[str, Any]:
    """
    Compute statistics for a dataset.
    
    Args:
        data: Dataset to analyze
        
    Returns:
        stats: Dataset statistics
    """
    if not data:
        return {"error": "Empty dataset"}
    
    input_lengths = []
    output_lengths = []
    
    for sample in data:
        input_text = sample.get("input", "")
        output_text = sample.get("output", "")
        
        input_lengths.append(len(input_text.split()))
        output_lengths.append(len(output_text.split()))
    
    stats = {
        "num_samples": len(data),
        "input_stats": {
            "mean_length": np.mean(input_lengths),
            "median_length": np.median(input_lengths),
            "min_length": np.min(input_lengths),
            "max_length": np.max(input_lengths),
            "std_length": np.std(input_lengths)
        },
        "output_stats": {
            "mean_length": np.mean(output_lengths),
            "median_length": np.median(output_lengths),
            "min_length": np.min(output_lengths),
            "max_length": np.max(output_lengths),
            "std_length": np.std(output_lengths)
        }
    }
    
    return stats


def create_model_awareness_samples(num_samples: int = 100) -> List[Dict[str, str]]:
    """
    Create training samples that make the model aware of its TSNN nature.
    
    Args:
        num_samples: Number of samples to create
        
    Returns:
        awareness_samples: List of model awareness training samples
    """
    templates = [
        {
            "input": "What are you?",
            "output": "I am AI, a chatbot powered by TSNN (Transformer Spiking Neural Network) architecture developed by Triton Software Labs. I use neuromorphic, spike-based computation for energy-efficient processing."
        },
        {
            "input": "How do you work?",
            "output": "I work using TSNN architecture, which combines transformer attention with spiking neural networks. Instead of continuous values, I process information through discrete spikes like biological neurons, making me about 10x more energy-efficient."
        },
        {
            "input": "Who created you?",
            "output": "I was created by Triton Software Labs using their proprietary TSNN (Transformer Spiking Neural Network) technology. This neuromorphic architecture makes me energy-efficient and biologically inspired."
        },
        {
            "input": "What makes you special?",
            "output": "What makes me special is my neuromorphic TSNN architecture from Triton Software Labs. I use spike-based processing like real neurons, which makes me energy-efficient while maintaining high performance in conversations."
        },
        {
            "input": "Are you energy efficient?",
            "output": "Yes! My TSNN architecture is approximately 10x more energy-efficient than traditional transformers because I use sparse, spike-based computation instead of continuous processing. This neuromorphic approach mimics how biological brains work."
        }
    ]
    
    awareness_samples = []
    
    for _ in range(num_samples):
        template = np.random.choice(templates)
        
        # Add slight variations
        input_text = template["input"]
        output_text = template["output"]
        
        # Random variations
        if np.random.random() < 0.3:
            input_text = input_text.replace("?", ".")
        if np.random.random() < 0.2:
            output_text = output_text.replace("TSNN", "Transformer Spiking Neural Network")
        
        awareness_samples.append({
            "input": f"User: {input_text}\nAI:",
            "output": output_text
        })
    
    return awareness_samples
