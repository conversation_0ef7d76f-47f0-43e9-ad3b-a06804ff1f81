#!/usr/bin/env python3
"""Test different generation strategies for TSNN"""

import torch
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tsnn.simple_tsnn import SimplifiedTSNNModel
from tsnn.tokenizer import TSNNTokenizer


def test_generation_strategies():
    """Test different generation approaches."""
    print("🧠 Testing TSNN Generation Strategies")
    print("=" * 50)
    
    # Load model
    model_path = "models/tsnn_improved_final.pt"
    checkpoint = torch.load(model_path, map_location='cpu')
    config = checkpoint.get('config', {})
    
    tokenizer = TSNNTokenizer()
    model = SimplifiedTSNNModel(
        vocab_size=config.get('vocab_size', tokenizer.vocab_size),
        d_model=config.get('d_model', 384),
        n_heads=config.get('n_heads', 6),
        num_layers=config.get('num_layers', 6),
        d_ff=config.get('d_ff', 1536),
        max_seq_len=config.get('max_seq_len', 256),
        dropout=config.get('dropout', 0.1)
    )
    
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    model = model.to(device)
    
    print(f"✅ Model loaded on {device}")
    
    # Test input
    test_input = "What are you?"
    context = f"User: {test_input}\nAI:"
    
    print(f"\n🧪 Testing input: '{test_input}'")
    print("=" * 30)
    
    # Test different generation strategies
    strategies = [
        {"name": "Greedy", "temperature": 1.0, "do_sample": False, "top_k": 0},
        {"name": "Low Temp", "temperature": 0.3, "do_sample": True, "top_k": 10},
        {"name": "Medium Temp", "temperature": 0.7, "do_sample": True, "top_k": 30},
        {"name": "High Temp", "temperature": 1.2, "do_sample": True, "top_k": 50},
        {"name": "Top-p", "temperature": 0.8, "do_sample": True, "top_k": 0, "top_p": 0.9},
    ]
    
    for strategy in strategies:
        print(f"\n🎯 Strategy: {strategy['name']}")
        
        try:
            # Tokenize
            encoding = tokenizer.encode(context, max_length=50, padding=False, truncation=True)
            input_ids = encoding["input_ids"].unsqueeze(0).to(device)
            
            print(f"📝 Input tokens: {input_ids.shape[1]}")
            print(f"🔤 Input text: '{context}'")
            
            # Generate with different strategies
            with torch.no_grad():
                generated = model.generate(
                    input_ids=input_ids,
                    max_length=input_ids.shape[1] + 20,
                    temperature=strategy["temperature"],
                    do_sample=strategy["do_sample"],
                    top_k=strategy.get("top_k", 50),
                    top_p=strategy.get("top_p", 1.0),
                    repetition_penalty=1.2,  # Add repetition penalty
                    pad_token_id=tokenizer.pad_token_id
                )
            
            # Decode
            generated_part = generated[:, input_ids.shape[1]:]
            print(f"🔢 Generated tokens: {generated_part[0].tolist()}")
            
            response = tokenizer.decode(generated_part[0], skip_special_tokens=True)
            print(f"📄 Raw response: '{response}'")
            
            # Clean response
            if "User:" in response:
                response = response.split("User:")[0].strip()
            if "AI:" in response:
                response = response.split("AI:")[-1].strip()
            
            response = " ".join(response.split())
            print(f"✨ Clean response: '{response}'")
            
        except Exception as e:
            print(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n🎉 Generation strategy test complete!")


def test_tokenizer_directly():
    """Test the tokenizer directly."""
    print("\n🔤 Testing Tokenizer Directly")
    print("=" * 30)
    
    tokenizer = TSNNTokenizer()
    
    test_texts = [
        "Hello world",
        "I am AI",
        "TSNN model",
        "Triton Software Labs"
    ]
    
    for text in test_texts:
        print(f"\n📝 Text: '{text}'")
        
        # Encode
        encoding = tokenizer.encode(text, add_special_tokens=False)
        tokens = encoding["input_ids"]
        print(f"🔢 Tokens: {tokens.tolist()}")
        
        # Show individual tokens
        individual_tokens = []
        for token_id in tokens:
            token = tokenizer.id_to_token.get(token_id.item(), "<UNK>")
            individual_tokens.append(token)
        print(f"🎯 Individual: {individual_tokens}")
        
        # Decode
        decoded = tokenizer.decode(tokens, skip_special_tokens=True)
        print(f"🔄 Decoded: '{decoded}'")


if __name__ == "__main__":
    test_generation_strategies()
    test_tokenizer_directly()
