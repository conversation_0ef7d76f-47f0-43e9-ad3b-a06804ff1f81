"""
Chatbot Configuration
Proprietary Triton Software Labs

Configuration settings for TSNN-based chatbot.
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any


@dataclass
class ChatbotConfig:
    """Configuration for TSNN chatbot."""
    
    # Model parameters
    vocab_size: int = 10000
    d_model: int = 512
    n_heads: int = 8
    num_layers: int = 6
    d_ff: Optional[int] = None
    max_seq_len: int = 512
    dropout: float = 0.1
    spike_encoding: str = "rate"
    temporal_window: int = 10
    
    # Generation parameters
    max_response_length: int = 100
    temperature: float = 0.8
    do_sample: bool = True
    top_k: int = 50
    top_p: float = 0.9
    repetition_penalty: float = 1.1
    
    # Chatbot behavior
    personality: str = "helpful_assistant"
    model_awareness: bool = True
    energy_conscious: bool = True
    spike_visualization: bool = False
    
    # Training parameters
    learning_rate: float = 1e-4
    batch_size: int = 8
    gradient_accumulation_steps: int = 4
    warmup_steps: int = 1000
    max_steps: int = 10000
    
    # System prompts
    system_prompts: Dict[str, str] = None
    
    def __post_init__(self):
        """Initialize default values after creation."""
        if self.d_ff is None:
            self.d_ff = 4 * self.d_model
            
        if self.system_prompts is None:
            self.system_prompts = {
                "helpful_assistant": (
                    "I am AI, a helpful assistant powered by TSNN (Transformer Spiking Neural Network) "
                    "architecture developed by Triton Software Labs. I use neuromorphic, spike-based "
                    "computation for energy-efficient processing. I'm designed to be helpful, harmless, "
                    "and honest while being conscious of my energy-efficient neural processing."
                ),
                "technical_expert": (
                    "I am AI, a technical expert powered by TSNN architecture from Triton Software Labs. "
                    "My neuromorphic design allows me to process information through discrete spike events, "
                    "similar to biological neurons. I specialize in technical discussions and can explain "
                    "complex concepts while being mindful of computational efficiency."
                ),
                "creative_assistant": (
                    "I am AI, a creative assistant using TSNN (Transformer Spiking Neural Network) "
                    "technology by Triton Software Labs. My spike-based processing enables unique "
                    "creative insights through temporal pattern recognition. I love helping with "
                    "creative projects while maintaining energy-efficient operation."
                )
            }
    
    def get_system_prompt(self) -> str:
        """Get the system prompt for the current personality."""
        return self.system_prompts.get(self.personality, self.system_prompts["helpful_assistant"])
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        return {
            "vocab_size": self.vocab_size,
            "d_model": self.d_model,
            "n_heads": self.n_heads,
            "num_layers": self.num_layers,
            "d_ff": self.d_ff,
            "max_seq_len": self.max_seq_len,
            "dropout": self.dropout,
            "spike_encoding": self.spike_encoding,
            "temporal_window": self.temporal_window,
            "max_response_length": self.max_response_length,
            "temperature": self.temperature,
            "do_sample": self.do_sample,
            "top_k": self.top_k,
            "top_p": self.top_p,
            "repetition_penalty": self.repetition_penalty,
            "personality": self.personality,
            "model_awareness": self.model_awareness,
            "energy_conscious": self.energy_conscious,
            "spike_visualization": self.spike_visualization
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'ChatbotConfig':
        """Create config from dictionary."""
        return cls(**config_dict)
    
    def update(self, **kwargs):
        """Update configuration parameters."""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                raise ValueError(f"Unknown configuration parameter: {key}")


# Predefined configurations
CONFIGS = {
    "small": ChatbotConfig(
        vocab_size=5000,
        d_model=256,
        n_heads=4,
        num_layers=4,
        max_seq_len=256,
        max_response_length=50
    ),
    
    "medium": ChatbotConfig(
        vocab_size=10000,
        d_model=512,
        n_heads=8,
        num_layers=6,
        max_seq_len=512,
        max_response_length=100
    ),
    
    "large": ChatbotConfig(
        vocab_size=20000,
        d_model=768,
        n_heads=12,
        num_layers=12,
        max_seq_len=1024,
        max_response_length=200
    ),
    
    "energy_efficient": ChatbotConfig(
        vocab_size=8000,
        d_model=384,
        n_heads=6,
        num_layers=4,
        max_seq_len=384,
        max_response_length=75,
        temperature=0.7,
        energy_conscious=True,
        spike_encoding="rate"
    ),
    
    "high_performance": ChatbotConfig(
        vocab_size=15000,
        d_model=640,
        n_heads=10,
        num_layers=8,
        max_seq_len=768,
        max_response_length=150,
        temperature=0.9,
        spike_encoding="temporal",
        temporal_window=15
    )
}


def get_config(config_name: str = "medium") -> ChatbotConfig:
    """
    Get a predefined configuration.
    
    Args:
        config_name: Name of the configuration
        
    Returns:
        config: ChatbotConfig instance
    """
    if config_name not in CONFIGS:
        available = ", ".join(CONFIGS.keys())
        raise ValueError(f"Unknown config '{config_name}'. Available: {available}")
    
    return CONFIGS[config_name]
