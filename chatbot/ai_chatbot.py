"""
AI Chatbot Implementation using TSNN
Proprietary Triton Software Labs

Main chatbot class that integrates TSNN model with conversation management
for neuromorphic conversational AI.
"""

import torch
import torch.nn.functional as F
from typing import Optional, Dict, Any, List
import time
import logging
from pathlib import Path

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tsnn.simple_tsnn import SimplifiedTSNNModel as TSNNModel
from tsnn.tokenizer import TSNNTokenizer
from .conversation import ConversationManager, Message
from .config import ChatbotConfig


class AIChatbot:
    """
    AI Chatbot powered by TSNN architecture.
    
    Features:
    - Neuromorphic spike-based processing
    - Energy-efficient conversation
    - Model awareness and self-identification
    - Conversation context management
    - Real-time spike visualization (optional)
    """
    
    def __init__(self, 
                 config: Optional[ChatbotConfig] = None,
                 model_path: Optional[str] = None,
                 device: Optional[str] = None):
        """
        Initialize AI chatbot.
        
        Args:
            config: Chatbot configuration
            model_path: Path to pre-trained model
            device: Device to run model on
        """
        self.config = config or ChatbotConfig()
        self.device = device or ("cuda" if torch.cuda.is_available() else "cpu")
        
        # Initialize logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Initialize tokenizer
        self.tokenizer = TSNNTokenizer()
        
        # Update config with actual vocab size
        self.config.vocab_size = self.tokenizer.vocab_size
        
        # Initialize TSNN model
        self.model = TSNNModel(
            vocab_size=self.config.vocab_size,
            d_model=self.config.d_model,
            n_heads=self.config.n_heads,
            num_layers=self.config.num_layers,
            d_ff=self.config.d_ff,
            max_seq_len=self.config.max_seq_len,
            dropout=self.config.dropout
        ).to(self.device)
        
        # Load pre-trained weights if provided
        if model_path and Path(model_path).exists():
            self.load_model(model_path)
        else:
            self.logger.info("No pre-trained model loaded. Using randomly initialized weights.")
            
        # Initialize conversation manager
        self.conversation = ConversationManager(
            energy_tracking=self.config.energy_conscious
        )
        
        # Add system message with model awareness
        if self.config.model_awareness:
            system_prompt = self.config.get_system_prompt()
            self.conversation.add_system_message(system_prompt)
            
        # Performance tracking
        self.total_inference_time = 0.0
        self.total_tokens_generated = 0
        self.spike_patterns_cache = {}
        
        self.logger.info(f"AI Chatbot initialized with TSNN architecture")
        self.logger.info(f"Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
        self.logger.info(f"Device: {self.device}")
        
    def load_model(self, model_path: str):
        """Load pre-trained model weights."""
        try:
            checkpoint = torch.load(model_path, map_location=self.device)
            if 'model_state_dict' in checkpoint:
                self.model.load_state_dict(checkpoint['model_state_dict'])
            else:
                self.model.load_state_dict(checkpoint)
            self.logger.info(f"Model loaded from {model_path}")
        except Exception as e:
            self.logger.error(f"Failed to load model from {model_path}: {e}")
            
    def save_model(self, model_path: str):
        """Save model weights."""
        try:
            torch.save({
                'model_state_dict': self.model.state_dict(),
                'config': self.config.to_dict(),
                'tokenizer_vocab': self.tokenizer.token_to_id
            }, model_path)
            self.logger.info(f"Model saved to {model_path}")
        except Exception as e:
            self.logger.error(f"Failed to save model to {model_path}: {e}")
            
    def _prepare_input(self, user_input: str) -> Dict[str, torch.Tensor]:
        """
        Prepare user input for model processing.

        Args:
            user_input: User's message

        Returns:
            model_input: Prepared input tensors
        """
        try:
            # Get conversation context (simplified)
            context_text = self.conversation.get_conversation_text(include_system=False)

            # Limit context length to avoid overflow
            max_context_length = self.config.max_seq_len - 100  # Leave room for response
            if len(context_text) > max_context_length:
                # Truncate from the beginning, keep recent context
                context_text = "..." + context_text[-max_context_length:]

            # Add current user input
            full_input = f"{context_text}\nUser: {user_input}\nAI:"

            # Tokenize with error handling
            encoding = self.tokenizer.encode(
                full_input,
                add_special_tokens=True,
                max_length=self.config.max_seq_len - 50,  # Leave buffer for generation
                padding=False,
                truncation=True
            )

            # Move to device
            input_ids = encoding["input_ids"].unsqueeze(0).to(self.device)
            attention_mask = encoding["attention_mask"].unsqueeze(0).to(self.device)

            # Validate tensor shapes
            if input_ids.shape[1] == 0:
                # Fallback for empty input
                fallback_text = f"User: {user_input}\nAI:"
                encoding = self.tokenizer.encode(fallback_text, add_special_tokens=True)
                input_ids = encoding["input_ids"].unsqueeze(0).to(self.device)
                attention_mask = encoding["attention_mask"].unsqueeze(0).to(self.device)

            return {
                "input_ids": input_ids,
                "attention_mask": attention_mask
            }

        except Exception as e:
            self.logger.error(f"Input preparation error: {e}")
            # Emergency fallback
            fallback_text = f"User: {user_input}\nAI:"
            encoding = self.tokenizer.encode(fallback_text, add_special_tokens=True, max_length=100)
            input_ids = encoding["input_ids"].unsqueeze(0).to(self.device)
            attention_mask = encoding["attention_mask"].unsqueeze(0).to(self.device)

            return {
                "input_ids": input_ids,
                "attention_mask": attention_mask
            }
    
    def _generate_response(self, model_input: Dict[str, torch.Tensor]) -> Dict[str, Any]:
        """
        Generate response using TSNN model.

        Args:
            model_input: Prepared input tensors

        Returns:
            generation_result: Generated response and metadata
        """
        start_time = time.time()

        try:
            with torch.no_grad():
                # Generate response with error handling
                generated_ids = self.model.generate(
                    input_ids=model_input["input_ids"],
                    max_length=min(
                        model_input["input_ids"].shape[1] + self.config.max_response_length,
                        self.config.max_seq_len - 10  # Leave some buffer
                    ),
                    temperature=self.config.temperature,
                    do_sample=self.config.do_sample,
                    top_k=self.config.top_k,
                    top_p=self.config.top_p,
                    pad_token_id=self.tokenizer.pad_token_id
                )

                # Extract only the generated part
                input_length = model_input["input_ids"].shape[1]
                if generated_ids.shape[1] > input_length:
                    generated_part = generated_ids[:, input_length:]
                else:
                    # Fallback if generation failed
                    generated_part = torch.tensor([[self.tokenizer.unk_token_id]], device=generated_ids.device)

                # Get spike patterns if visualization is enabled
                spike_patterns = None
                if self.config.spike_visualization:
                    try:
                        model_output = self.model(
                            model_input["input_ids"],
                            attention_mask=model_input["attention_mask"]
                        )
                        spike_patterns = model_output.get("last_hidden_state")
                    except:
                        spike_patterns = None

            generation_time = time.time() - start_time

            # Decode response with error handling
            try:
                response_text = self.tokenizer.decode(
                    generated_part[0],
                    skip_special_tokens=True
                ).strip()
            except Exception as e:
                self.logger.error(f"Decode error: {e}")
                response_text = "I'm having trouble generating a response right now."

            # Clean up response (remove any remaining context)
            if "AI:" in response_text:
                response_text = response_text.split("AI:")[-1].strip()
            if "User:" in response_text:
                response_text = response_text.split("User:")[0].strip()

            # Fallback for empty responses
            if not response_text or len(response_text.strip()) < 2:
                response_text = "I'm still learning to respond properly with my TSNN architecture!"

            return {
                "response": response_text,
                "generation_time": generation_time,
                "tokens_generated": generated_part.shape[1] if generated_part.numel() > 0 else 0,
                "spike_patterns": spike_patterns
            }

        except Exception as e:
            self.logger.error(f"Generation error: {e}")
            return {
                "response": "I apologize, but I encountered an error while processing your message. As a TSNN model, I'm still learning to handle all types of inputs efficiently.",
                "generation_time": time.time() - start_time,
                "tokens_generated": 0,
                "spike_patterns": None
            }
    
    def chat(self, user_input: str) -> str:
        """
        Main chat interface.
        
        Args:
            user_input: User's message
            
        Returns:
            response: AI's response
        """
        try:
            # Add user message to conversation
            self.conversation.add_message("user", user_input)
            
            # Prepare input for model
            model_input = self._prepare_input(user_input)
            
            # Generate response
            generation_result = self._generate_response(model_input)
            
            # Calculate energy cost
            energy_cost = None
            if self.config.energy_conscious:
                energy_cost = self.conversation.estimate_energy_cost(
                    input_length=model_input["input_ids"].shape[1],
                    output_length=generation_result["tokens_generated"],
                    model_size="medium"  # Could be made configurable
                )
            
            # Add assistant message to conversation
            self.conversation.add_message(
                "assistant", 
                generation_result["response"],
                spike_patterns=generation_result["spike_patterns"],
                energy_cost=energy_cost
            )
            
            # Update performance tracking
            self.total_inference_time += generation_result["generation_time"]
            self.total_tokens_generated += generation_result["tokens_generated"]
            
            # Log performance info
            if self.config.energy_conscious and energy_cost:
                self.logger.info(
                    f"Response generated in {generation_result['generation_time']:.2f}s, "
                    f"Energy cost: {energy_cost:.4f}"
                )
            
            return generation_result["response"]
            
        except Exception as e:
            self.logger.error(f"Error during chat: {e}")
            error_response = (
                "I apologize, but I encountered an error while processing your message. "
                "As a TSNN model, I'm still learning to handle all types of inputs efficiently."
            )
            self.conversation.add_message("assistant", error_response)
            return error_response
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the TSNN model."""
        model_info = self.model.get_model_info()
        model_info.update({
            "parameters": sum(p.numel() for p in self.model.parameters()),
            "device": str(self.device),
            "config": self.config.to_dict()
        })
        return model_info
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        avg_time_per_token = (
            self.total_inference_time / max(self.total_tokens_generated, 1)
        )
        
        conversation_stats = self.conversation.get_conversation_stats()
        
        return {
            "total_inference_time": self.total_inference_time,
            "total_tokens_generated": self.total_tokens_generated,
            "avg_time_per_token": avg_time_per_token,
            "conversation_stats": conversation_stats
        }
    
    def reset_conversation(self):
        """Reset the conversation history."""
        self.conversation.clear_conversation()
        
        # Re-add system message
        if self.config.model_awareness:
            system_prompt = self.config.get_system_prompt()
            self.conversation.add_system_message(system_prompt)
            
        self.logger.info("Conversation reset")
    
    def save_conversation(self, filepath: str):
        """Save current conversation to file."""
        self.conversation.save_conversation(filepath)
        
    def load_conversation(self, filepath: str):
        """Load conversation from file."""
        self.conversation.load_conversation(filepath)
        
    def set_personality(self, personality: str):
        """
        Change chatbot personality.
        
        Args:
            personality: New personality type
        """
        if personality in self.config.system_prompts:
            self.config.personality = personality
            
            # Update system message
            new_system_prompt = self.config.get_system_prompt()
            
            # Remove old system messages and add new one
            self.conversation.messages = [
                m for m in self.conversation.messages if m.role != "system"
            ]
            self.conversation.add_system_message(new_system_prompt)
            
            self.logger.info(f"Personality changed to: {personality}")
        else:
            available = ", ".join(self.config.system_prompts.keys())
            raise ValueError(f"Unknown personality '{personality}'. Available: {available}")
    
    def explain_tsnn(self) -> str:
        """Provide explanation of TSNN architecture."""
        explanation = (
            "I am powered by TSNN (Transformer Spiking Neural Network) architecture, "
            "a proprietary technology developed by Triton Software Labs. Here's how I work:\n\n"
            "🧠 **Neuromorphic Processing**: Unlike traditional AI models that use continuous "
            "values, I process information through discrete 'spikes' - just like real neurons "
            "in the brain.\n\n"
            "⚡ **Energy Efficiency**: My spike-based computation is approximately 10x more "
            "energy-efficient than traditional transformers, making me environmentally friendly.\n\n"
            "🔄 **Temporal Dynamics**: I can process temporal patterns and sequences more "
            "naturally due to my biological-inspired architecture.\n\n"
            "🎯 **Event-Driven**: I only process information when 'spikes' occur, leading to "
            "sparse, efficient computation.\n\n"
            "This makes me unique among AI assistants - I combine the power of transformer "
            "attention mechanisms with the efficiency and biological plausibility of spiking "
            "neural networks!"
        )
        return explanation
