"""
Conversation Management for TSNN Chatbot
Proprietary Triton Software Labs

Manages conversation history, context, and spike-aware processing.
"""

import torch
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime
import json


@dataclass
class Message:
    """Represents a single message in the conversation."""
    role: str  # "user", "assistant", "system"
    content: str
    timestamp: datetime
    spike_patterns: Optional[torch.Tensor] = None
    energy_cost: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary for serialization."""
        return {
            "role": self.role,
            "content": self.content,
            "timestamp": self.timestamp.isoformat(),
            "energy_cost": self.energy_cost
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """Create message from dictionary."""
        return cls(
            role=data["role"],
            content=data["content"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            energy_cost=data.get("energy_cost")
        )


class ConversationManager:
    """
    Manages conversation state and history for TSNN chatbot.
    
    Features:
    - Conversation history tracking
    - Context window management
    - Spike pattern caching
    - Energy consumption monitoring
    - Temporal conversation dynamics
    """
    
    def __init__(self, 
                 max_history: int = 50,
                 context_window: int = 10,
                 energy_tracking: bool = True):
        self.max_history = max_history
        self.context_window = context_window
        self.energy_tracking = energy_tracking
        
        # Conversation state
        self.messages: List[Message] = []
        self.conversation_id = self._generate_conversation_id()
        self.total_energy_cost = 0.0
        self.spike_cache: Dict[str, torch.Tensor] = {}
        
        # Conversation metadata
        self.start_time = datetime.now()
        self.last_activity = datetime.now()
        self.turn_count = 0
        
    def _generate_conversation_id(self) -> str:
        """Generate unique conversation ID."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"tsnn_conv_{timestamp}"
    
    def add_message(self, 
                   role: str, 
                   content: str,
                   spike_patterns: Optional[torch.Tensor] = None,
                   energy_cost: Optional[float] = None) -> Message:
        """
        Add a message to the conversation.
        
        Args:
            role: Message role ("user", "assistant", "system")
            content: Message content
            spike_patterns: Associated spike patterns
            energy_cost: Energy cost of processing this message
            
        Returns:
            message: Created message object
        """
        message = Message(
            role=role,
            content=content,
            timestamp=datetime.now(),
            spike_patterns=spike_patterns,
            energy_cost=energy_cost
        )
        
        self.messages.append(message)
        self.last_activity = datetime.now()
        
        if role == "user":
            self.turn_count += 1
            
        # Track energy consumption
        if energy_cost and self.energy_tracking:
            self.total_energy_cost += energy_cost
            
        # Manage history length
        if len(self.messages) > self.max_history:
            # Remove oldest messages but keep system messages
            non_system_messages = [m for m in self.messages if m.role != "system"]
            system_messages = [m for m in self.messages if m.role == "system"]
            
            if len(non_system_messages) > self.max_history - len(system_messages):
                excess = len(non_system_messages) - (self.max_history - len(system_messages))
                non_system_messages = non_system_messages[excess:]
                
            self.messages = system_messages + non_system_messages
            
        return message
    
    def get_context_messages(self, include_system: bool = True) -> List[Message]:
        """
        Get messages within the current context window.
        
        Args:
            include_system: Whether to include system messages
            
        Returns:
            context_messages: Messages within context window
        """
        if include_system:
            # Get system messages plus recent conversation
            system_messages = [m for m in self.messages if m.role == "system"]
            recent_messages = [m for m in self.messages if m.role != "system"][-self.context_window:]
            return system_messages + recent_messages
        else:
            return self.messages[-self.context_window:]
    
    def get_conversation_text(self, include_system: bool = True) -> str:
        """
        Get conversation as formatted text.
        
        Args:
            include_system: Whether to include system messages
            
        Returns:
            conversation_text: Formatted conversation string
        """
        context_messages = self.get_context_messages(include_system)
        
        conversation_parts = []
        for message in context_messages:
            if message.role == "system":
                conversation_parts.append(f"System: {message.content}")
            elif message.role == "user":
                conversation_parts.append(f"User: {message.content}")
            elif message.role == "assistant":
                conversation_parts.append(f"AI: {message.content}")
                
        return "\n".join(conversation_parts)
    
    def get_spike_context(self) -> Optional[torch.Tensor]:
        """
        Get aggregated spike patterns from recent context.
        
        Returns:
            spike_context: Aggregated spike patterns or None
        """
        context_messages = self.get_context_messages(include_system=False)
        spike_patterns = []
        
        for message in context_messages:
            if message.spike_patterns is not None:
                spike_patterns.append(message.spike_patterns)
                
        if spike_patterns:
            # Aggregate spike patterns (simple concatenation for now)
            return torch.cat(spike_patterns, dim=1)  # Concatenate along sequence dimension
        
        return None
    
    def estimate_energy_cost(self, 
                           input_length: int, 
                           output_length: int,
                           model_size: str = "medium") -> float:
        """
        Estimate energy cost for processing.
        
        Args:
            input_length: Length of input sequence
            output_length: Length of output sequence
            model_size: Size of the model
            
        Returns:
            energy_cost: Estimated energy cost in arbitrary units
        """
        # Energy cost factors for different model sizes
        size_factors = {
            "small": 0.5,
            "medium": 1.0,
            "large": 2.0
        }
        
        base_factor = size_factors.get(model_size, 1.0)
        
        # TSNN is more energy efficient than traditional transformers
        # Spike-based computation reduces energy by ~10x
        tsnn_efficiency = 0.1
        
        # Estimate based on sequence lengths and operations
        input_cost = input_length * 0.01 * base_factor * tsnn_efficiency
        output_cost = output_length * 0.02 * base_factor * tsnn_efficiency  # Generation is more expensive
        attention_cost = (input_length * output_length) * 0.001 * base_factor * tsnn_efficiency
        
        total_cost = input_cost + output_cost + attention_cost
        return total_cost
    
    def get_conversation_stats(self) -> Dict[str, Any]:
        """
        Get conversation statistics.
        
        Returns:
            stats: Dictionary of conversation statistics
        """
        duration = (self.last_activity - self.start_time).total_seconds()
        
        user_messages = [m for m in self.messages if m.role == "user"]
        assistant_messages = [m for m in self.messages if m.role == "assistant"]
        
        avg_energy_per_turn = (
            self.total_energy_cost / max(self.turn_count, 1) 
            if self.energy_tracking else 0.0
        )
        
        return {
            "conversation_id": self.conversation_id,
            "duration_seconds": duration,
            "total_messages": len(self.messages),
            "user_messages": len(user_messages),
            "assistant_messages": len(assistant_messages),
            "turn_count": self.turn_count,
            "total_energy_cost": self.total_energy_cost,
            "avg_energy_per_turn": avg_energy_per_turn,
            "start_time": self.start_time.isoformat(),
            "last_activity": self.last_activity.isoformat()
        }
    
    def save_conversation(self, filepath: str):
        """
        Save conversation to file.
        
        Args:
            filepath: Path to save conversation
        """
        conversation_data = {
            "conversation_id": self.conversation_id,
            "start_time": self.start_time.isoformat(),
            "last_activity": self.last_activity.isoformat(),
            "total_energy_cost": self.total_energy_cost,
            "turn_count": self.turn_count,
            "messages": [msg.to_dict() for msg in self.messages],
            "stats": self.get_conversation_stats()
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(conversation_data, f, indent=2, ensure_ascii=False)
    
    def load_conversation(self, filepath: str):
        """
        Load conversation from file.
        
        Args:
            filepath: Path to load conversation from
        """
        with open(filepath, 'r', encoding='utf-8') as f:
            conversation_data = json.load(f)
            
        self.conversation_id = conversation_data["conversation_id"]
        self.start_time = datetime.fromisoformat(conversation_data["start_time"])
        self.last_activity = datetime.fromisoformat(conversation_data["last_activity"])
        self.total_energy_cost = conversation_data["total_energy_cost"]
        self.turn_count = conversation_data["turn_count"]
        
        self.messages = [
            Message.from_dict(msg_data) 
            for msg_data in conversation_data["messages"]
        ]
    
    def clear_conversation(self):
        """Clear conversation history."""
        self.messages.clear()
        self.conversation_id = self._generate_conversation_id()
        self.total_energy_cost = 0.0
        self.spike_cache.clear()
        self.start_time = datetime.now()
        self.last_activity = datetime.now()
        self.turn_count = 0
    
    def add_system_message(self, content: str):
        """
        Add a system message to the conversation.
        
        Args:
            content: System message content
        """
        self.add_message("system", content)
    
    def get_last_user_message(self) -> Optional[Message]:
        """Get the last user message."""
        user_messages = [m for m in self.messages if m.role == "user"]
        return user_messages[-1] if user_messages else None
    
    def get_last_assistant_message(self) -> Optional[Message]:
        """Get the last assistant message."""
        assistant_messages = [m for m in self.messages if m.role == "assistant"]
        return assistant_messages[-1] if assistant_messages else None
