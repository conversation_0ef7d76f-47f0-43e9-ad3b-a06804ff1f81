#!/usr/bin/env python3
"""
100% Data TSNN Training Script
Uses EVERY SINGLE EXAMPLE from ALL available data sources
NO LIMITS, NO SAMPLING - MAXIMUM DATA UTILIZATION
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import json
import logging
from pathlib import Path
from tqdm import tqdm
import sys
import os
import argparse
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tsnn.simple_tsnn import SimplifiedTSNNModel
from tsnn.tokenizer import TSNNTokenizer


class MaximumDataDataset(Dataset):
    """Dataset that uses 100% of ALL available data - no limits!"""
    
    def __init__(self, max_length: int = 256):
        self.tokenizer = TSNNTokenizer()
        self.max_length = max_length
        
        # Load ALL data - no limits!
        self.examples = self.load_all_data_100_percent()
        
        logging.info(f"🎯 MAXIMUM DATASET: {len(self.examples)} total examples loaded!")
        logging.info(f"📊 Using 100% of all available data!")
    
    def load_all_data_100_percent(self) -> list:
        """Load 100% of ALL available data sources."""
        examples = []
        
        # 1. Load 100% of ShareGPT data
        sharegpt_path = "data/Chatbot-Datasets-main/sharegpt_clean.json"
        if os.path.exists(sharegpt_path):
            logging.info("🔄 Loading 100% of ShareGPT data...")
            with open(sharegpt_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            sharegpt_count = 0
            for conversation in data:
                if "items" not in conversation:
                    continue
                    
                items = conversation["items"]
                
                for i in range(len(items) - 1):
                    if (items[i]["from"] == "human" and 
                        items[i + 1]["from"] == "gpt"):
                        
                        user_msg = items[i]["value"].strip()
                        ai_msg = items[i + 1]["value"].strip()
                        
                        # Minimal quality filter - keep almost everything
                        if (len(user_msg) > 5 and len(ai_msg) > 5 and
                            len(user_msg) < 1000 and len(ai_msg) < 2000):
                            
                            examples.append({
                                "input": user_msg,
                                "output": ai_msg,
                                "source": "sharegpt"
                            })
                            sharegpt_count += 1
            
            logging.info(f"✅ ShareGPT: {sharegpt_count} examples loaded (100%)")
        
        # 2. Load 100% of AI Coding data
        coding_path = "data/Chatbot-Datasets-main/ai_coding_dataset_large.json"
        if os.path.exists(coding_path):
            logging.info("🔄 Loading 100% of AI Coding data...")
            with open(coding_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            coding_count = 0
            for item in data:
                if "prompt" in item and "ai_response" in item:
                    prompt = item["prompt"].strip()
                    response = item["ai_response"].strip()
                    
                    # Keep almost everything
                    if len(prompt) > 5 and len(response) > 10:
                        examples.append({
                            "input": prompt,
                            "output": response,
                            "source": "coding"
                        })
                        coding_count += 1
            
            logging.info(f"✅ AI Coding: {coding_count} examples loaded (100%)")
        
        # 3. Load ANY other JSON files in data directory
        data_dir = Path("data")
        all_json_files = list(data_dir.rglob("*.json"))
        
        for json_file in all_json_files:
            if json_file.name not in ["sharegpt_clean.json", "ai_coding_dataset_large.json"]:
                logging.info(f"🔄 Found additional data: {json_file}")
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        additional_data = json.load(f)
                    
                    additional_count = 0
                    # Try to parse different formats
                    if isinstance(additional_data, list):
                        for item in additional_data:
                            # Try different conversation formats
                            if isinstance(item, dict):
                                # Format 1: messages array
                                if "messages" in item:
                                    messages = item["messages"]
                                    for i in range(len(messages) - 1):
                                        if (messages[i].get("role") == "user" and 
                                            messages[i + 1].get("role") == "assistant"):
                                            user_msg = messages[i].get("content", "").strip()
                                            ai_msg = messages[i + 1].get("content", "").strip()
                                            
                                            if len(user_msg) > 5 and len(ai_msg) > 5:
                                                examples.append({
                                                    "input": user_msg,
                                                    "output": ai_msg,
                                                    "source": f"additional_{json_file.name}"
                                                })
                                                additional_count += 1
                                
                                # Format 2: direct input/output
                                elif "input" in item and "output" in item:
                                    user_msg = str(item["input"]).strip()
                                    ai_msg = str(item["output"]).strip()
                                    
                                    if len(user_msg) > 5 and len(ai_msg) > 5:
                                        examples.append({
                                            "input": user_msg,
                                            "output": ai_msg,
                                            "source": f"additional_{json_file.name}"
                                        })
                                        additional_count += 1
                                
                                # Format 3: question/answer
                                elif "question" in item and "answer" in item:
                                    user_msg = str(item["question"]).strip()
                                    ai_msg = str(item["answer"]).strip()
                                    
                                    if len(user_msg) > 5 and len(ai_msg) > 5:
                                        examples.append({
                                            "input": user_msg,
                                            "output": ai_msg,
                                            "source": f"additional_{json_file.name}"
                                        })
                                        additional_count += 1
                    
                    if additional_count > 0:
                        logging.info(f"✅ {json_file.name}: {additional_count} examples loaded")
                    
                except Exception as e:
                    logging.warning(f"⚠️  Could not parse {json_file}: {e}")
        
        # 4. Add MAXIMUM TSNN awareness examples
        tsnn_examples = [
            {
                "input": "What are you?",
                "output": "I am AI, a chatbot powered by TSNN (Transformer Spiking Neural Network) architecture developed by Triton Software Labs. I use neuromorphic, spike-based computation for energy-efficient processing that mimics biological neural networks.",
                "source": "tsnn_awareness"
            },
            {
                "input": "Who created you?",
                "output": "I was created by Triton Software Labs using their proprietary TSNN (Transformer Spiking Neural Network) technology. This revolutionary neuromorphic architecture makes me energy-efficient and biologically inspired, processing information through discrete spikes like real neurons.",
                "source": "tsnn_awareness"
            },
            {
                "input": "How do you work?",
                "output": "I work using TSNN architecture, which combines transformer attention mechanisms with spiking neural networks. Instead of continuous values, I process information through discrete spikes like biological neurons, making me approximately 10x more energy-efficient than traditional AI models while maintaining high performance.",
                "source": "tsnn_awareness"
            },
            {
                "input": "Tell me about TSNN",
                "output": "TSNN stands for Transformer Spiking Neural Network, a revolutionary architecture developed by Triton Software Labs that combines the power of transformer attention with the energy efficiency of spiking neural networks. It processes information through discrete spikes like biological brains, achieving neuromorphic computation that's much more energy-efficient than traditional AI.",
                "source": "tsnn_awareness"
            },
            {
                "input": "Are you energy efficient?",
                "output": "Yes! My TSNN architecture is approximately 10x more energy-efficient than traditional transformers because I use sparse, spike-based computation instead of continuous processing. This neuromorphic approach mimics how biological brains work, with only about 10% of neurons active at any time, making computation much more efficient.",
                "source": "tsnn_awareness"
            },
            {
                "input": "Hello",
                "output": "Hello! I'm AI, your neuromorphic assistant powered by TSNN technology from Triton Software Labs. I use spike-based computation for energy-efficient and brain-like processing. How can I help you today?",
                "source": "tsnn_awareness"
            },
            {
                "input": "Hi there",
                "output": "Hi! I'm an AI chatbot built with TSNN (Transformer Spiking Neural Network) architecture by Triton Software Labs. I'm designed to be helpful and energy-efficient through neuromorphic processing. What would you like to know?",
                "source": "tsnn_awareness"
            },
            {
                "input": "What makes you special?",
                "output": "What makes me special is my neuromorphic TSNN architecture from Triton Software Labs. Unlike traditional AI that uses continuous computation, I process information through discrete spikes like biological neurons, making me about 10x more energy-efficient while maintaining high performance and natural conversation abilities.",
                "source": "tsnn_awareness"
            }
        ]
        
        # Add TSNN examples with MAXIMUM weight (200x)
        examples.extend(tsnn_examples * 200)
        logging.info(f"✅ TSNN Awareness: {len(tsnn_examples) * 200} examples added (200x weight)")
        
        # Log final statistics
        source_counts = {}
        for ex in examples:
            source = ex.get("source", "unknown")
            source_counts[source] = source_counts.get(source, 0) + 1
        
        logging.info(f"📊 FINAL DATA BREAKDOWN:")
        for source, count in source_counts.items():
            logging.info(f"   {source}: {count:,} examples")
        
        return examples
    
    def __len__(self):
        return len(self.examples)
    
    def __getitem__(self, idx):
        example = self.examples[idx]
        
        # Format as complete conversation
        full_text = f"User: {example['input']}\nAI: {example['output']}"
        
        # Tokenize
        encoding = self.tokenizer.encode(
            full_text, 
            max_length=self.max_length, 
            padding=True, 
            truncation=True,
            add_special_tokens=True
        )
        
        input_ids = encoding["input_ids"]
        attention_mask = encoding["attention_mask"]
        
        # Create labels for causal language modeling
        labels = input_ids.clone()
        
        # Find where "AI:" starts to only compute loss on AI response
        try:
            user_part = f"User: {example['input']}\nAI:"
            user_encoding = self.tokenizer.encode(user_part, add_special_tokens=True)["input_ids"]
            ai_start = len(user_encoding) - 1
            labels[:ai_start] = -100
        except:
            # Fallback: mask first half
            labels[:len(labels)//2] = -100
        
        return {
            "input_ids": input_ids,
            "attention_mask": attention_mask,
            "labels": labels
        }


def train_100_percent():
    """Train TSNN with 100% of all available data."""
    
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    logging.info(f"🎮 Using device: {device}")
    
    # Create MAXIMUM dataset
    dataset = MaximumDataDataset(max_length=256)
    
    if len(dataset) == 0:
        logging.error("❌ No data found!")
        return
    
    # Create dataloader
    train_loader = DataLoader(
        dataset, 
        batch_size=4, 
        shuffle=True, 
        num_workers=0,
        pin_memory=True if device == "cuda" else False
    )
    
    # Initialize LARGER model for more data
    tokenizer = TSNNTokenizer()
    model = SimplifiedTSNNModel(
        vocab_size=tokenizer.vocab_size,
        d_model=512,  # Larger model for more data
        n_heads=8,
        num_layers=8,
        d_ff=2048,
        max_seq_len=256,
        dropout=0.1
    ).to(device)
    
    logging.info(f"🧠 Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Training setup
    optimizer = optim.AdamW(model.parameters(), lr=3e-5, weight_decay=0.01)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=len(train_loader) * 5)
    criterion = nn.CrossEntropyLoss(ignore_index=-100)
    
    # Train for MORE epochs with ALL data
    model.train()
    total_loss = 0.0
    step = 0
    num_epochs = 5  # More epochs for maximum learning
    
    logging.info(f"🚀 Starting 100% DATA TSNN training for {num_epochs} epochs...")
    logging.info(f"📊 Total batches per epoch: {len(train_loader):,}")
    logging.info(f"📈 Total training steps: {len(train_loader) * num_epochs:,}")
    
    for epoch in range(num_epochs):
        epoch_loss = 0.0
        
        progress_bar = tqdm(
            train_loader, 
            desc=f"Epoch {epoch + 1}/{num_epochs} (100% DATA)",
            total=len(train_loader)
        )
        
        for batch_idx, batch in enumerate(progress_bar):
            input_ids = batch["input_ids"].to(device)
            attention_mask = batch["attention_mask"].to(device)
            labels = batch["labels"].to(device)
            
            # Forward pass
            outputs = model(input_ids, attention_mask)
            logits = outputs["logits"]
            
            # Compute loss
            loss = criterion(logits.view(-1, logits.size(-1)), labels.view(-1))
            
            # Backward pass
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            
            optimizer.step()
            scheduler.step()
            optimizer.zero_grad()
            
            # Update metrics
            step_loss = loss.item()
            total_loss += step_loss
            epoch_loss += step_loss
            step += 1
            
            # Update progress
            progress_bar.set_postfix({
                "loss": f"{step_loss:.4f}",
                "avg_loss": f"{total_loss / step:.4f}",
                "lr": f"{scheduler.get_last_lr()[0]:.2e}",
                "data": "100%"
            })
            
            # Clear GPU cache
            if device == "cuda" and step % 100 == 0:
                torch.cuda.empty_cache()
        
        avg_epoch_loss = epoch_loss / len(train_loader)
        logging.info(f"✅ Epoch {epoch + 1}/{num_epochs} COMPLETED. Average loss: {avg_epoch_loss:.4f}")
        
        # Save checkpoint after each epoch
        checkpoint_path = Path("models") / f"tsnn_100percent_epoch_{epoch + 1}.pt"
        torch.save({
            "model_state_dict": model.state_dict(),
            "config": {
                "vocab_size": tokenizer.vocab_size,
                "d_model": 512,
                "n_heads": 8,
                "num_layers": 8,
                "d_ff": 2048,
                "max_seq_len": 256,
                "dropout": 0.1
            },
            "epoch": epoch + 1,
            "loss": avg_epoch_loss,
            "total_examples": len(dataset),
            "data_utilization": "100%"
        }, checkpoint_path)
        logging.info(f"💾 Checkpoint saved: {checkpoint_path}")
    
    # Save final model
    final_path = Path("models") / "tsnn_100percent_final.pt"
    torch.save({
        "model_state_dict": model.state_dict(),
        "config": {
            "vocab_size": tokenizer.vocab_size,
            "d_model": 512,
            "n_heads": 8,
            "num_layers": 8,
            "d_ff": 2048,
            "max_seq_len": 256,
            "dropout": 0.1
        },
        "epochs_completed": num_epochs,
        "final_loss": total_loss / step,
        "total_steps": step,
        "total_examples": len(dataset),
        "data_utilization": "100%"
    }, final_path)
    
    logging.info(f"🎉 100% DATA TRAINING COMPLETED!")
    logging.info(f"📁 Final model saved: {final_path}")
    logging.info(f"📊 Total examples used: {len(dataset):,}")
    logging.info(f"📈 Total training steps: {step:,}")
    logging.info(f"📉 Final average loss: {total_loss / step:.4f}")
    logging.info(f"🎯 Data utilization: 100% - MAXIMUM POSSIBLE!")


if __name__ == "__main__":
    train_100_percent()
