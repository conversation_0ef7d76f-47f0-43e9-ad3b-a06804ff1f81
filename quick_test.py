#!/usr/bin/env python3
"""Quick test of the TSNN chatbot"""

import torch
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tsnn.simple_tsnn import SimplifiedTSNNModel
from tsnn.tokenizer import TSNNTokenizer


def quick_chat_test():
    """Quick test of the chatbot functionality."""
    print("🧠 Quick TSNN Chat Test")
    print("=" * 30)
    
    # Load model
    model_path = "models/tsnn_chatbot_final.pt"
    if not os.path.exists(model_path):
        print("❌ Model not found")
        return
    
    print("📁 Loading model...")
    checkpoint = torch.load(model_path, map_location='cpu')
    config = checkpoint.get('config', {})
    
    tokenizer = TSNNTokenizer()
    model = SimplifiedTSNNModel(
        vocab_size=config.get('vocab_size', tokenizer.vocab_size),
        d_model=config.get('d_model', 256),
        n_heads=config.get('n_heads', 4),
        num_layers=config.get('num_layers', 4),
        d_ff=config.get('d_ff', 1024),
        max_seq_len=config.get('max_seq_len', 256),
        dropout=config.get('dropout', 0.1)
    )
    
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    print("✅ Model loaded")
    
    # Test conversations
    test_inputs = ["hello", "what are you", "how do you work"]
    
    for user_input in test_inputs:
        print(f"\n👤 User: {user_input}")
        
        try:
            # Simple context
            context = f"User: {user_input}\nAI:"
            
            # Tokenize
            encoding = tokenizer.encode(context, max_length=50, padding=False, truncation=True)
            input_ids = encoding["input_ids"].unsqueeze(0)
            
            # Generate
            with torch.no_grad():
                generated = model.generate(
                    input_ids=input_ids,
                    max_length=input_ids.shape[1] + 20,
                    temperature=0.7,
                    do_sample=True,
                    top_k=30
                )
            
            # Decode
            generated_part = generated[:, input_ids.shape[1]:]
            response = tokenizer.decode(generated_part[0], skip_special_tokens=True)
            
            # Clean
            if "User:" in response:
                response = response.split("User:")[0].strip()
            if "AI:" in response:
                response = response.split("AI:")[-1].strip()
            
            print(f"🤖 AI: {response}")
            
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print(f"\n🎉 Test complete!")


if __name__ == "__main__":
    quick_chat_test()
