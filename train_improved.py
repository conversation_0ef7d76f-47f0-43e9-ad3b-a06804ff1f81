#!/usr/bin/env python3
"""
Improved TSNN Training Script
Uses ALL available data with better preprocessing for coherent responses
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import json
import logging
from pathlib import Path
from tqdm import tqdm
import numpy as np
from typing import List, Dict, Any, Optional, Union
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tsnn.simple_tsnn import SimplifiedTSNNModel
from tsnn.tokenizer import TSNNTokenizer
from chatbot.config import get_config


class ImprovedConversationDataset(Dataset):
    """Improved dataset with better text preprocessing."""
    
    def __init__(self, 
                 data_paths: List[str],
                 tokenizer: TSNNTokenizer,
                 max_length: int = 256,
                 model_awareness_prob: float = 0.2):
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.model_awareness_prob = model_awareness_prob
        
        # Load and preprocess all data
        self.examples = self.create_improved_examples(data_paths)
        
        logging.info(f"Created {len(self.examples)} improved training examples")
    
    def create_improved_examples(self, data_paths: List[str]) -> List[Dict[str, str]]:
        """Create improved training examples with better formatting."""
        examples = []
        
        for data_path in data_paths:
            logging.info(f"Processing {data_path}...")
            
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if isinstance(data, list):
                for item in data:
                    if "messages" in item:
                        # Standard conversation format
                        messages = item["messages"]
                        for i, message in enumerate(messages):
                            if message["role"] == "assistant":
                                # Find the user message before this
                                user_msg = None
                                for j in range(i-1, -1, -1):
                                    if messages[j]["role"] == "user":
                                        user_msg = messages[j]["content"]
                                        break
                                
                                if user_msg:
                                    examples.append({
                                        "input": user_msg.strip(),
                                        "output": message["content"].strip()
                                    })
                    
                    elif "prompt" in item and "ai_response" in item:
                        # AI coding dataset format
                        examples.append({
                            "input": item["prompt"].strip(),
                            "output": item["ai_response"].strip()
                        })
        
        # Add model awareness examples
        awareness_examples = self.create_model_awareness_examples()
        examples.extend(awareness_examples)
        
        # Filter out very short or very long examples
        filtered_examples = []
        for ex in examples:
            input_len = len(ex["input"].split())
            output_len = len(ex["output"].split())
            
            if (5 <= input_len <= 50 and 5 <= output_len <= 100 and 
                len(ex["input"]) > 10 and len(ex["output"]) > 10):
                filtered_examples.append(ex)
        
        logging.info(f"Filtered to {len(filtered_examples)} high-quality examples")
        return filtered_examples
    
    def create_model_awareness_examples(self) -> List[Dict[str, str]]:
        """Create examples that teach the model about its TSNN nature."""
        awareness_examples = [
            {
                "input": "What are you?",
                "output": "I am AI, a chatbot powered by TSNN (Transformer Spiking Neural Network) architecture developed by Triton Software Labs. I use neuromorphic, spike-based computation for energy-efficient processing."
            },
            {
                "input": "Who created you?",
                "output": "I was created by Triton Software Labs using their proprietary TSNN (Transformer Spiking Neural Network) technology. This neuromorphic architecture makes me energy-efficient and biologically inspired."
            },
            {
                "input": "How do you work?",
                "output": "I work using TSNN architecture, which combines transformer attention mechanisms with spiking neural networks. Instead of continuous values, I process information through discrete spikes like biological neurons, making me about 10x more energy-efficient than traditional AI models."
            },
            {
                "input": "What makes you special?",
                "output": "What makes me special is my neuromorphic TSNN architecture from Triton Software Labs. I use spike-based processing like real neurons, which makes me energy-efficient while maintaining high performance in conversations."
            },
            {
                "input": "Tell me about TSNN",
                "output": "TSNN stands for Transformer Spiking Neural Network, a revolutionary architecture that combines the power of transformer attention with the energy efficiency of spiking neural networks. It processes information through discrete spikes like biological brains, making it much more energy-efficient than traditional AI."
            },
            {
                "input": "Are you energy efficient?",
                "output": "Yes! My TSNN architecture is approximately 10x more energy-efficient than traditional transformers because I use sparse, spike-based computation instead of continuous processing. This neuromorphic approach mimics how biological brains work."
            },
            {
                "input": "Hello",
                "output": "Hello! I'm AI, your neuromorphic assistant powered by TSNN technology from Triton Software Labs. I use spike-based computation for energy-efficient and brain-like processing. How can I help you today?"
            },
            {
                "input": "Hi there",
                "output": "Hi! I'm AI, a TSNN-powered chatbot that uses neuromorphic processing. I'm designed to be helpful while being energy-efficient through spike-based computation. What would you like to know?"
            }
        ]
        
        # Duplicate awareness examples to increase their weight
        return awareness_examples * 10  # 10x more weight for model awareness
    
    def __len__(self) -> int:
        return len(self.examples)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        example = self.examples[idx]
        
        # Create input-output pair with clear formatting
        input_text = f"User: {example['input']}\nAI:"
        target_text = f" {example['output']}"
        
        # Tokenize input
        input_encoding = self.tokenizer.encode(
            input_text,
            add_special_tokens=True,
            max_length=self.max_length - 50,  # Leave room for target
            padding=False,
            truncation=True
        )
        
        # Tokenize target
        target_encoding = self.tokenizer.encode(
            target_text,
            add_special_tokens=False,
            max_length=50,
            padding=False,
            truncation=True
        )
        
        # Combine input and target for language modeling
        combined_ids = torch.cat([input_encoding["input_ids"], target_encoding["input_ids"]])
        combined_mask = torch.cat([input_encoding["attention_mask"], target_encoding["attention_mask"]])
        
        # Create labels (shift by one for causal LM)
        labels = combined_ids.clone()
        labels[:len(input_encoding["input_ids"])] = -100  # Ignore input tokens in loss
        
        # Pad to max_length
        if len(combined_ids) < self.max_length:
            pad_length = self.max_length - len(combined_ids)
            combined_ids = torch.cat([combined_ids, torch.zeros(pad_length, dtype=torch.long)])
            combined_mask = torch.cat([combined_mask, torch.zeros(pad_length, dtype=torch.long)])
            labels = torch.cat([labels, torch.full((pad_length,), -100, dtype=torch.long)])
        
        return {
            "input_ids": combined_ids[:self.max_length],
            "attention_mask": combined_mask[:self.max_length],
            "labels": labels[:self.max_length]
        }


def train_improved_model():
    """Train an improved TSNN model with all available data."""
    
    # Setup logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # Find all JSON files
    data_dir = Path("data")
    json_files = list(data_dir.rglob("*.json"))
    data_paths = [str(f) for f in json_files]
    
    logging.info(f"Found {len(data_paths)} data files:")
    for path in data_paths:
        logging.info(f"  - {path}")
    
    # Setup device
    device = "cuda" if torch.cuda.is_available() else "cpu"
    logging.info(f"Using device: {device}")
    
    # Initialize tokenizer and model
    tokenizer = TSNNTokenizer()
    
    model = SimplifiedTSNNModel(
        vocab_size=tokenizer.vocab_size,
        d_model=384,  # Slightly larger
        n_heads=6,
        num_layers=6,  # More layers
        d_ff=1536,
        max_seq_len=256,
        dropout=0.1
    ).to(device)
    
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Create improved dataset
    dataset = ImprovedConversationDataset(
        data_paths=data_paths,
        tokenizer=tokenizer,
        max_length=256
    )
    
    # Create dataloader
    train_loader = DataLoader(
        dataset,
        batch_size=4,  # Larger batch size
        shuffle=True,
        num_workers=0
    )
    
    # Setup training
    optimizer = optim.AdamW(model.parameters(), lr=5e-5, weight_decay=0.01)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=3000)
    criterion = nn.CrossEntropyLoss(ignore_index=-100)
    
    # Training loop
    model.train()
    total_loss = 0.0
    step = 0
    max_steps = 3000
    
    logging.info("Starting improved TSNN training...")
    
    for epoch in range(10):  # More epochs
        epoch_loss = 0.0
        
        progress_bar = tqdm(train_loader, desc=f"Epoch {epoch + 1}")
        
        for batch in progress_bar:
            if step >= max_steps:
                break
                
            # Move to device
            input_ids = batch["input_ids"].to(device)
            attention_mask = batch["attention_mask"].to(device)
            labels = batch["labels"].to(device)
            
            # Forward pass
            outputs = model(input_ids, attention_mask)
            logits = outputs["logits"]
            
            # Compute loss
            loss = criterion(logits.view(-1, logits.size(-1)), labels.view(-1))
            
            # Backward pass
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            
            optimizer.step()
            scheduler.step()
            optimizer.zero_grad()
            
            # Update metrics
            step_loss = loss.item()
            total_loss += step_loss
            epoch_loss += step_loss
            step += 1
            
            # Update progress
            progress_bar.set_postfix({
                "loss": f"{step_loss:.4f}",
                "avg_loss": f"{total_loss / step:.4f}",
                "lr": f"{scheduler.get_last_lr()[0]:.2e}"
            })
            
            # Clear GPU cache periodically
            if device == "cuda" and step % 100 == 0:
                torch.cuda.empty_cache()
        
        if step >= max_steps:
            break
            
        logging.info(f"Epoch {epoch + 1} completed. Average loss: {epoch_loss / len(train_loader):.4f}")
    
    # Save improved model
    output_dir = Path("models")
    output_dir.mkdir(exist_ok=True)
    
    model_path = output_dir / "tsnn_improved_final.pt"
    torch.save({
        "model_state_dict": model.state_dict(),
        "config": {
            "vocab_size": tokenizer.vocab_size,
            "d_model": 384,
            "n_heads": 6,
            "num_layers": 6,
            "d_ff": 1536,
            "max_seq_len": 256,
            "dropout": 0.1
        },
        "tokenizer_vocab": tokenizer.token_to_id
    }, model_path)
    
    logging.info(f"Improved TSNN model saved to: {model_path}")
    logging.info("Training completed!")


if __name__ == "__main__":
    train_improved_model()
