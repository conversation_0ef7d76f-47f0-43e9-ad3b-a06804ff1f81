#!/usr/bin/env python3
"""
Debug TSNN Generation
Let's see exactly what's happening during generation
"""

import torch
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tsnn.simple_tsnn import SimplifiedTSNNModel
from tsnn.tokenizer import TSNNTokenizer


def debug_generation():
    """Debug the generation process step by step."""
    print("🔍 Debugging TSNN Generation")
    print("=" * 50)
    
    # Load model
    model_path = "models/tsnn_full_final.pt"
    checkpoint = torch.load(model_path, map_location='cpu')
    config = checkpoint.get('config', {})
    
    tokenizer = TSNNTokenizer()
    model = SimplifiedTSNNModel(
        vocab_size=config.get('vocab_size', tokenizer.vocab_size),
        d_model=config.get('d_model', 512),
        n_heads=config.get('n_heads', 8),
        num_layers=config.get('num_layers', 8),
        d_ff=config.get('d_ff', 2048),
        max_seq_len=config.get('max_seq_len', 256),
        dropout=config.get('dropout', 0.1)
    )
    
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    print("✅ Model loaded")
    
    # Test input
    user_input = "Hello"
    context = f"User: {user_input}\nAI:"
    
    print(f"\n🧪 Testing input: '{user_input}'")
    print(f"📝 Context: '{context}'")
    
    # Tokenize
    encoding = tokenizer.encode(context, max_length=50, padding=False, truncation=True)
    input_ids = encoding["input_ids"]
    
    print(f"\n🔢 Input tokens: {input_ids.tolist()}")
    print(f"📄 Input length: {len(input_ids)}")
    
    # Show what each token represents
    print("\n🎯 Token breakdown:")
    for i, token_id in enumerate(input_ids):
        token = tokenizer.id_to_token.get(token_id.item(), "<UNK>")
        print(f"  {i}: {token_id.item()} -> '{token}'")
    
    # Test forward pass
    input_ids_batch = input_ids.unsqueeze(0)
    
    print(f"\n⚡ Testing forward pass...")
    with torch.no_grad():
        outputs = model(input_ids_batch)
        logits = outputs["logits"]
        
    print(f"✅ Forward pass successful")
    print(f"📊 Logits shape: {logits.shape}")
    
    # Check the last token's predictions
    last_logits = logits[0, -1, :]  # Last position, all vocab
    top_probs, top_indices = torch.topk(torch.softmax(last_logits, dim=-1), 10)
    
    print(f"\n🎯 Top 10 next token predictions:")
    for i, (prob, idx) in enumerate(zip(top_probs, top_indices)):
        token = tokenizer.id_to_token.get(idx.item(), "<UNK>")
        print(f"  {i+1}: '{token}' (ID: {idx.item()}, prob: {prob.item():.4f})")
    
    # Test manual generation step by step
    print(f"\n🔄 Manual generation (step by step):")
    
    generated = input_ids_batch.clone()
    max_new_tokens = 10
    
    for step in range(max_new_tokens):
        print(f"\nStep {step + 1}:")
        
        # Forward pass
        with torch.no_grad():
            outputs = model(generated)
            logits = outputs["logits"]
        
        # Get next token logits
        next_token_logits = logits[:, -1, :] / 1.0  # temperature = 1.0
        
        # Show top predictions
        probs = torch.softmax(next_token_logits, dim=-1)
        top_probs, top_indices = torch.topk(probs, 5)
        
        print(f"  Top 5 predictions:")
        for prob, idx in zip(top_probs[0], top_indices[0]):
            token = tokenizer.id_to_token.get(idx.item(), "<UNK>")
            print(f"    '{token}' (prob: {prob.item():.4f})")
        
        # Sample next token (greedy for debugging)
        next_token = torch.argmax(next_token_logits, dim=-1, keepdim=True)
        next_token_str = tokenizer.id_to_token.get(next_token.item(), "<UNK>")
        
        print(f"  Selected: '{next_token_str}' (ID: {next_token.item()})")
        
        # Add to sequence
        generated = torch.cat([generated, next_token], dim=1)
        
        # Decode current sequence
        current_text = tokenizer.decode(generated[0], skip_special_tokens=True)
        print(f"  Current text: '{current_text}'")
        
        # Stop if we hit a repetitive pattern
        if step > 2:
            last_3_tokens = generated[0, -3:].tolist()
            if len(set(last_3_tokens)) == 1:
                print(f"  ⚠️  Repetitive pattern detected: {last_3_tokens}")
                break
    
    print(f"\n🎉 Debug complete!")


def test_different_inputs():
    """Test with different inputs to see if it's always the same."""
    print("\n🧪 Testing Different Inputs")
    print("=" * 30)
    
    # Load model quickly
    model_path = "models/tsnn_full_final.pt"
    checkpoint = torch.load(model_path, map_location='cpu')
    config = checkpoint.get('config', {})
    
    tokenizer = TSNNTokenizer()
    model = SimplifiedTSNNModel(
        vocab_size=config.get('vocab_size', tokenizer.vocab_size),
        d_model=config.get('d_model', 512),
        n_heads=config.get('n_heads', 8),
        num_layers=config.get('num_layers', 8),
        d_ff=config.get('d_ff', 2048),
        max_seq_len=config.get('max_seq_len', 256),
        dropout=config.get('dropout', 0.1)
    )
    
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    test_inputs = ["Hello", "Hi", "What", "How", "Why"]
    
    for test_input in test_inputs:
        context = f"User: {test_input}\nAI:"
        encoding = tokenizer.encode(context, max_length=50, padding=False, truncation=True)
        input_ids = encoding["input_ids"].unsqueeze(0)
        
        # Generate just 5 tokens
        with torch.no_grad():
            outputs = model(input_ids)
            logits = outputs["logits"]
            next_token_logits = logits[:, -1, :]
            next_token = torch.argmax(next_token_logits, dim=-1)
            
        next_token_str = tokenizer.id_to_token.get(next_token.item(), "<UNK>")
        print(f"'{test_input}' -> next token: '{next_token_str}' (ID: {next_token.item()})")


if __name__ == "__main__":
    debug_generation()
    test_different_inputs()
