#!/usr/bin/env python3
"""
Full TSNN Training Script - Complete Epochs
Trains for complete epochs without artificial step limits
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import json
import logging
from pathlib import Path
from tqdm import tqdm
import numpy as np
from typing import List, Dict, Any
import sys
import os
import argparse
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tsnn.simple_tsnn import SimplifiedTSNNModel
from tsnn.tokenizer import TSNNTokenizer


class FullConversationDataset(Dataset):
    """Dataset that loads all your clean data efficiently."""
    
    def __init__(self, max_length: int = 256, max_samples: int = None):
        self.tokenizer = TSNNTokenizer()
        self.max_length = max_length
        self.max_samples = max_samples
        
        # Load data
        self.examples = self.load_all_data()
        
        logging.info(f"Dataset ready with {len(self.examples)} examples")
    
    def load_all_data(self) -> List[Dict[str, str]]:
        """Load all available data."""
        examples = []
        
        # Load ShareGPT data
        sharegpt_path = "data/Chatbot-Datasets-main/sharegpt_clean.json"
        if os.path.exists(sharegpt_path):
            logging.info("Loading ShareGPT data...")
            with open(sharegpt_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            count = 0
            for conversation in data:
                if self.max_samples and count >= self.max_samples:
                    break
                    
                if "items" not in conversation:
                    continue
                    
                items = conversation["items"]
                
                # Process conversation turns
                for i in range(len(items) - 1):
                    if (items[i]["from"] == "human" and 
                        items[i + 1]["from"] == "gpt"):
                        
                        user_msg = items[i]["value"].strip()
                        ai_msg = items[i + 1]["value"].strip()
                        
                        # Basic quality filter
                        if (len(user_msg) > 10 and len(ai_msg) > 10 and
                            len(user_msg.split()) < 100 and len(ai_msg.split()) < 200):
                            
                            examples.append({
                                "input": user_msg,
                                "output": ai_msg
                            })
                            count += 1
                            
                            if self.max_samples and count >= self.max_samples:
                                break
            
            logging.info(f"Loaded {count} examples from ShareGPT")
        
        # Load coding data
        coding_path = "data/Chatbot-Datasets-main/ai_coding_dataset_large.json"
        if os.path.exists(coding_path):
            logging.info("Loading coding data...")
            with open(coding_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            coding_count = 0
            for item in data:
                if "prompt" in item and "ai_response" in item:
                    prompt = item["prompt"].strip()
                    response = item["ai_response"].strip()
                    
                    if len(prompt) > 10 and len(response) > 20:
                        examples.append({
                            "input": prompt,
                            "output": response
                        })
                        coding_count += 1
            
            logging.info(f"Loaded {coding_count} examples from coding dataset")
        
        # Add TSNN awareness (high weight)
        tsnn_examples = [
            {
                "input": "What are you?",
                "output": "I am AI, a chatbot powered by TSNN (Transformer Spiking Neural Network) architecture developed by Triton Software Labs. I use neuromorphic, spike-based computation for energy-efficient processing."
            },
            {
                "input": "Who created you?",
                "output": "I was created by Triton Software Labs using their proprietary TSNN (Transformer Spiking Neural Network) technology. This neuromorphic architecture makes me energy-efficient and biologically inspired."
            },
            {
                "input": "How do you work?",
                "output": "I work using TSNN architecture, which combines transformer attention mechanisms with spiking neural networks. Instead of continuous values, I process information through discrete spikes like biological neurons, making me about 10x more energy-efficient than traditional AI models."
            },
            {
                "input": "Tell me about TSNN",
                "output": "TSNN stands for Transformer Spiking Neural Network, a revolutionary architecture that combines the power of transformer attention with the energy efficiency of spiking neural networks. It processes information through discrete spikes like biological brains, making it much more energy-efficient than traditional AI."
            },
            {
                "input": "Hello",
                "output": "Hello! I'm AI, your neuromorphic assistant powered by TSNN technology from Triton Software Labs. I use spike-based computation for energy-efficient and brain-like processing. How can I help you today?"
            }
        ]
        
        # Add TSNN examples with high weight (50x)
        examples.extend(tsnn_examples * 50)
        
        logging.info(f"Total examples: {len(examples)}")
        return examples
    
    def __len__(self) -> int:
        return len(self.examples)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        example = self.examples[idx]
        
        # Format as conversation
        input_text = f"User: {example['input']}\nAI:"
        target_text = f" {example['output']}"
        
        # Tokenize
        input_encoding = self.tokenizer.encode(
            input_text, max_length=self.max_length - 60, padding=False, truncation=True
        )
        target_encoding = self.tokenizer.encode(
            target_text, max_length=60, padding=False, truncation=True
        )
        
        # Combine
        combined_ids = torch.cat([input_encoding["input_ids"], target_encoding["input_ids"]])
        combined_mask = torch.cat([input_encoding["attention_mask"], target_encoding["attention_mask"]])
        
        # Create labels
        labels = combined_ids.clone()
        labels[:len(input_encoding["input_ids"])] = -100
        
        # Pad
        if len(combined_ids) < self.max_length:
            pad_length = self.max_length - len(combined_ids)
            combined_ids = torch.cat([combined_ids, torch.zeros(pad_length, dtype=torch.long)])
            combined_mask = torch.cat([combined_mask, torch.zeros(pad_length, dtype=torch.long)])
            labels = torch.cat([labels, torch.full((pad_length,), -100, dtype=torch.long)])
        
        return {
            "input_ids": combined_ids[:self.max_length],
            "attention_mask": combined_mask[:self.max_length],
            "labels": labels[:self.max_length]
        }


def train_full_tsnn(args):
    """Train TSNN for complete epochs."""
    
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # Setup device
    device = "cuda" if torch.cuda.is_available() else "cpu"
    logging.info(f"Using device: {device}")
    
    # Create dataset
    dataset = FullConversationDataset(max_length=256, max_samples=args.max_samples)
    
    if len(dataset) == 0:
        logging.error("No training data found!")
        return
    
    # Create dataloader
    train_loader = DataLoader(
        dataset, 
        batch_size=args.batch_size, 
        shuffle=True, 
        num_workers=0,
        pin_memory=True if device == "cuda" else False
    )
    
    # Initialize model
    tokenizer = TSNNTokenizer()
    model = SimplifiedTSNNModel(
        vocab_size=tokenizer.vocab_size,
        d_model=args.d_model,
        n_heads=args.n_heads,
        num_layers=args.num_layers,
        d_ff=args.d_model * 4,
        max_seq_len=256,
        dropout=0.1
    ).to(device)
    
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Setup training
    optimizer = optim.AdamW(model.parameters(), lr=args.learning_rate, weight_decay=0.01)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=len(train_loader) * args.epochs)
    criterion = nn.CrossEntropyLoss(ignore_index=-100)
    
    # Training loop
    model.train()
    total_loss = 0.0
    step = 0
    
    logging.info(f"Starting FULL TSNN training for {args.epochs} complete epochs...")
    logging.info(f"Total batches per epoch: {len(train_loader)}")
    logging.info(f"Total training steps: {len(train_loader) * args.epochs}")
    
    for epoch in range(args.epochs):
        epoch_loss = 0.0
        
        progress_bar = tqdm(
            train_loader, 
            desc=f"Epoch {epoch + 1}/{args.epochs}",
            total=len(train_loader)
        )
        
        for batch_idx, batch in enumerate(progress_bar):
            # Move to device
            input_ids = batch["input_ids"].to(device)
            attention_mask = batch["attention_mask"].to(device)
            labels = batch["labels"].to(device)
            
            # Forward pass
            outputs = model(input_ids, attention_mask)
            logits = outputs["logits"]
            
            # Compute loss
            loss = criterion(logits.view(-1, logits.size(-1)), labels.view(-1))
            
            # Backward pass
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            
            optimizer.step()
            scheduler.step()
            optimizer.zero_grad()
            
            # Update metrics
            step_loss = loss.item()
            total_loss += step_loss
            epoch_loss += step_loss
            step += 1
            
            # Update progress
            progress_bar.set_postfix({
                "loss": f"{step_loss:.4f}",
                "avg_loss": f"{total_loss / step:.4f}",
                "lr": f"{scheduler.get_last_lr()[0]:.2e}",
                "progress": f"{batch_idx + 1}/{len(train_loader)}"
            })
            
            # Clear GPU cache periodically
            if device == "cuda" and step % 100 == 0:
                torch.cuda.empty_cache()
        
        avg_epoch_loss = epoch_loss / len(train_loader)
        logging.info(f"Epoch {epoch + 1}/{args.epochs} COMPLETED. Average loss: {avg_epoch_loss:.4f}")
        
        # Save checkpoint after each epoch
        checkpoint_path = Path("models") / f"tsnn_full_epoch_{epoch + 1}.pt"
        torch.save({
            "model_state_dict": model.state_dict(),
            "config": {
                "vocab_size": tokenizer.vocab_size,
                "d_model": args.d_model,
                "n_heads": args.n_heads,
                "num_layers": args.num_layers,
                "d_ff": args.d_model * 4,
                "max_seq_len": 256,
                "dropout": 0.1
            },
            "epoch": epoch + 1,
            "loss": avg_epoch_loss,
            "total_steps": step
        }, checkpoint_path)
        logging.info(f"Checkpoint saved: {checkpoint_path}")
    
    # Save final model
    final_path = Path("models") / "tsnn_full_final.pt"
    torch.save({
        "model_state_dict": model.state_dict(),
        "config": {
            "vocab_size": tokenizer.vocab_size,
            "d_model": args.d_model,
            "n_heads": args.n_heads,
            "num_layers": args.num_layers,
            "d_ff": args.d_model * 4,
            "max_seq_len": 256,
            "dropout": 0.1
        },
        "epochs_completed": args.epochs,
        "final_loss": total_loss / step,
        "total_steps": step
    }, final_path)
    
    logging.info(f"🎉 FULL TRAINING COMPLETED!")
    logging.info(f"📁 Final model saved: {final_path}")
    logging.info(f"📊 Total steps: {step}")
    logging.info(f"📉 Final average loss: {total_loss / step:.4f}")


def main():
    parser = argparse.ArgumentParser(description="Full TSNN Training")
    parser.add_argument("--epochs", type=int, default=2, help="Number of complete epochs")
    parser.add_argument("--batch-size", type=int, default=4, help="Batch size")
    parser.add_argument("--learning-rate", type=float, default=3e-5, help="Learning rate")
    parser.add_argument("--max-samples", type=int, default=50000, help="Max samples to use")
    parser.add_argument("--d-model", type=int, default=512, help="Model dimension")
    parser.add_argument("--n-heads", type=int, default=8, help="Number of attention heads")
    parser.add_argument("--num-layers", type=int, default=8, help="Number of layers")
    
    args = parser.parse_args()
    
    train_full_tsnn(args)


if __name__ == "__main__":
    main()
