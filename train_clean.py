#!/usr/bin/env python3
"""
Clean TSNN Training Script
Handles sharegpt_clean.json and ai_coding_dataset_large.json properly
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import json
import logging
from pathlib import Path
from tqdm import tqdm
import numpy as np
from typing import List, Dict, Any
import sys
import os
import re
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tsnn.simple_tsnn import SimplifiedTSNNModel
from tsnn.tokenizer import TSNNTokenizer


class CleanConversationDataset(Dataset):
    """Clean dataset for high-quality TSNN training."""
    
    def __init__(self, max_length: int = 256):
        self.tokenizer = TSNNTokenizer()
        self.max_length = max_length
        
        # Load and process data
        self.examples = self.load_clean_data()
        
        logging.info(f"Created {len(self.examples)} clean training examples")
    
    def load_clean_data(self) -> List[Dict[str, str]]:
        """Load and clean data from both sources."""
        examples = []
        
        # Load ShareGPT clean data
        sharegpt_path = "data/Chatbot-Datasets-main/sharegpt_clean.json"
        if os.path.exists(sharegpt_path):
            examples.extend(self.process_sharegpt(sharegpt_path))
        
        # Load AI coding data
        coding_path = "data/Chatbot-Datasets-main/ai_coding_dataset_large.json"
        if os.path.exists(coding_path):
            examples.extend(self.process_coding_data(coding_path))
        
        # Add TSNN awareness examples
        examples.extend(self.create_tsnn_awareness())
        
        # Filter and clean examples
        return self.filter_examples(examples)
    
    def process_sharegpt(self, file_path: str) -> List[Dict[str, str]]:
        """Process ShareGPT clean data."""
        logging.info(f"Processing ShareGPT data: {file_path}")
        examples = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        for conversation in data:
            if "items" not in conversation:
                continue
                
            items = conversation["items"]
            
            # Process conversation turns
            for i in range(len(items) - 1):
                if (items[i]["from"] == "human" and 
                    items[i + 1]["from"] == "gpt"):
                    
                    user_msg = items[i]["value"].strip()
                    ai_msg = items[i + 1]["value"].strip()
                    
                    # Skip if too short or contains non-English
                    if (len(user_msg) < 10 or len(ai_msg) < 10 or
                        self.contains_non_english(user_msg) or
                        self.contains_non_english(ai_msg)):
                        continue
                    
                    examples.append({
                        "input": user_msg,
                        "output": ai_msg,
                        "source": "sharegpt"
                    })
        
        logging.info(f"Extracted {len(examples)} examples from ShareGPT")
        return examples
    
    def process_coding_data(self, file_path: str) -> List[Dict[str, str]]:
        """Process AI coding dataset."""
        logging.info(f"Processing coding data: {file_path}")
        examples = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        for item in data:
            if "prompt" in item and "ai_response" in item:
                prompt = item["prompt"].strip()
                response = item["ai_response"].strip()
                
                # Skip if too short or looks broken
                if (len(prompt) < 10 or len(response) < 20 or
                    "def example():" in response or
                    response.count("#") > 3):  # Skip broken examples
                    continue
                
                examples.append({
                    "input": prompt,
                    "output": response,
                    "source": "coding"
                })
        
        logging.info(f"Extracted {len(examples)} examples from coding dataset")
        return examples
    
    def create_tsnn_awareness(self) -> List[Dict[str, str]]:
        """Create TSNN awareness examples."""
        awareness = [
            {
                "input": "What are you?",
                "output": "I am AI, a chatbot powered by TSNN (Transformer Spiking Neural Network) architecture developed by Triton Software Labs. I use neuromorphic, spike-based computation for energy-efficient processing.",
                "source": "tsnn_awareness"
            },
            {
                "input": "Who created you?",
                "output": "I was created by Triton Software Labs using their proprietary TSNN (Transformer Spiking Neural Network) technology. This neuromorphic architecture makes me energy-efficient and biologically inspired.",
                "source": "tsnn_awareness"
            },
            {
                "input": "How do you work?",
                "output": "I work using TSNN architecture, which combines transformer attention mechanisms with spiking neural networks. Instead of continuous values, I process information through discrete spikes like biological neurons, making me about 10x more energy-efficient than traditional AI models.",
                "source": "tsnn_awareness"
            },
            {
                "input": "Tell me about TSNN",
                "output": "TSNN stands for Transformer Spiking Neural Network, a revolutionary architecture that combines the power of transformer attention with the energy efficiency of spiking neural networks. It processes information through discrete spikes like biological brains, making it much more energy-efficient than traditional AI.",
                "source": "tsnn_awareness"
            },
            {
                "input": "Are you energy efficient?",
                "output": "Yes! My TSNN architecture is approximately 10x more energy-efficient than traditional transformers because I use sparse, spike-based computation instead of continuous processing. This neuromorphic approach mimics how biological brains work.",
                "source": "tsnn_awareness"
            },
            {
                "input": "Hello",
                "output": "Hello! I'm AI, your neuromorphic assistant powered by TSNN technology from Triton Software Labs. I use spike-based computation for energy-efficient and brain-like processing. How can I help you today?",
                "source": "tsnn_awareness"
            }
        ]
        
        # Multiply awareness examples for higher weight
        return awareness * 20  # 20x weight for TSNN awareness
    
    def contains_non_english(self, text: str) -> bool:
        """Check if text contains significant non-English content."""
        # Simple heuristic: if more than 20% non-ASCII, skip
        non_ascii = sum(1 for c in text if ord(c) > 127)
        return non_ascii / len(text) > 0.2
    
    def filter_examples(self, examples: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """Filter examples for quality."""
        filtered = []
        
        for ex in examples:
            input_text = ex["input"]
            output_text = ex["output"]
            
            # Length checks
            if (len(input_text.split()) < 3 or len(input_text.split()) > 100 or
                len(output_text.split()) < 5 or len(output_text.split()) > 200):
                continue
            
            # Quality checks
            if (input_text.lower() == output_text.lower() or
                len(set(input_text.split())) < 3 or
                len(set(output_text.split())) < 5):
                continue
            
            filtered.append(ex)
        
        logging.info(f"Filtered to {len(filtered)} high-quality examples")
        return filtered
    
    def __len__(self) -> int:
        return len(self.examples)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        example = self.examples[idx]
        
        # Format as conversation
        input_text = f"User: {example['input']}\nAI:"
        target_text = f" {example['output']}"
        
        # Tokenize
        input_encoding = self.tokenizer.encode(
            input_text, max_length=self.max_length - 60, padding=False, truncation=True
        )
        target_encoding = self.tokenizer.encode(
            target_text, max_length=60, padding=False, truncation=True
        )
        
        # Combine for language modeling
        combined_ids = torch.cat([input_encoding["input_ids"], target_encoding["input_ids"]])
        combined_mask = torch.cat([input_encoding["attention_mask"], target_encoding["attention_mask"]])
        
        # Create labels (ignore input tokens in loss)
        labels = combined_ids.clone()
        labels[:len(input_encoding["input_ids"])] = -100
        
        # Pad to max_length
        if len(combined_ids) < self.max_length:
            pad_length = self.max_length - len(combined_ids)
            combined_ids = torch.cat([combined_ids, torch.zeros(pad_length, dtype=torch.long)])
            combined_mask = torch.cat([combined_mask, torch.zeros(pad_length, dtype=torch.long)])
            labels = torch.cat([labels, torch.full((pad_length,), -100, dtype=torch.long)])
        
        return {
            "input_ids": combined_ids[:self.max_length],
            "attention_mask": combined_mask[:self.max_length],
            "labels": labels[:self.max_length]
        }


def train_clean_tsnn():
    """Train TSNN with clean, high-quality data."""
    
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # Setup device
    device = "cuda" if torch.cuda.is_available() else "cpu"
    logging.info(f"Using device: {device}")
    
    # Create dataset
    dataset = CleanConversationDataset(max_length=256)
    
    if len(dataset) == 0:
        logging.error("No training data found!")
        return
    
    # Create dataloader
    train_loader = DataLoader(dataset, batch_size=4, shuffle=True, num_workers=0)
    
    # Initialize model
    tokenizer = TSNNTokenizer()
    model = SimplifiedTSNNModel(
        vocab_size=tokenizer.vocab_size,
        d_model=512,  # Larger model
        n_heads=8,
        num_layers=8,
        d_ff=2048,
        max_seq_len=256,
        dropout=0.1
    ).to(device)
    
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Setup training
    optimizer = optim.AdamW(model.parameters(), lr=3e-5, weight_decay=0.01)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=5000)
    criterion = nn.CrossEntropyLoss(ignore_index=-100)
    
    # Training loop
    model.train()
    total_loss = 0.0
    step = 0
    num_epochs = 3  # Complete epochs instead of step limit

    logging.info(f"Starting clean TSNN training for {num_epochs} complete epochs...")

    for epoch in range(num_epochs):
        epoch_loss = 0.0

        progress_bar = tqdm(train_loader, desc=f"Epoch {epoch + 1}/{num_epochs}")

        for batch in progress_bar:
            
            # Move to device
            input_ids = batch["input_ids"].to(device)
            attention_mask = batch["attention_mask"].to(device)
            labels = batch["labels"].to(device)
            
            # Forward pass
            outputs = model(input_ids, attention_mask)
            logits = outputs["logits"]
            
            # Compute loss
            loss = criterion(logits.view(-1, logits.size(-1)), labels.view(-1))
            
            # Backward pass
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            
            optimizer.step()
            scheduler.step()
            optimizer.zero_grad()
            
            # Update metrics
            step_loss = loss.item()
            total_loss += step_loss
            epoch_loss += step_loss
            step += 1
            
            progress_bar.set_postfix({
                "loss": f"{step_loss:.4f}",
                "avg_loss": f"{total_loss / step:.4f}",
                "lr": f"{scheduler.get_last_lr()[0]:.2e}"
            })
            
            # Clear GPU cache
            if device == "cuda" and step % 100 == 0:
                torch.cuda.empty_cache()

        logging.info(f"Epoch {epoch + 1}/{num_epochs} completed. Average loss: {epoch_loss / len(train_loader):.4f}")

        # Save checkpoint after each epoch
        checkpoint_path = Path("models") / f"tsnn_clean_epoch_{epoch + 1}.pt"
        torch.save({
            "model_state_dict": model.state_dict(),
            "config": {
                "vocab_size": tokenizer.vocab_size,
                "d_model": 512,
                "n_heads": 8,
                "num_layers": 8,
                "d_ff": 2048,
                "max_seq_len": 256,
                "dropout": 0.1
            },
            "epoch": epoch + 1,
            "loss": epoch_loss / len(train_loader)
        }, checkpoint_path)
        logging.info(f"Checkpoint saved: {checkpoint_path}")
    
    # Save model
    output_dir = Path("models")
    output_dir.mkdir(exist_ok=True)
    
    model_path = output_dir / "tsnn_clean_final.pt"
    torch.save({
        "model_state_dict": model.state_dict(),
        "config": {
            "vocab_size": tokenizer.vocab_size,
            "d_model": 512,
            "n_heads": 8,
            "num_layers": 8,
            "d_ff": 2048,
            "max_seq_len": 256,
            "dropout": 0.1
        },
        "tokenizer_vocab": tokenizer.token_to_id
    }, model_path)
    
    logging.info(f"Clean TSNN model saved to: {model_path}")
    logging.info("Training completed!")


if __name__ == "__main__":
    train_clean_tsnn()
