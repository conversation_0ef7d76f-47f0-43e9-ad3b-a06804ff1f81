#!/usr/bin/env python3
"""
Data Inspection Script for TSNN Training Data
Proprietary Triton Software Labs

Inspect and analyze the structure of your training datasets.
"""

import json
import argparse
from pathlib import Path
from typing import Dict, Any, List
import logging


def analyze_json_structure(file_path: Path) -> Dict[str, Any]:
    """Analyze the structure of a JSON file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        analysis = {
            "file_path": str(file_path),
            "file_size_mb": file_path.stat().st_size / (1024 * 1024),
            "data_type": type(data).__name__,
            "total_items": 0,
            "sample_structure": None,
            "detected_format": "unknown",
            "fields": set()
        }
        
        if isinstance(data, list):
            analysis["total_items"] = len(data)
            if data:
                first_item = data[0]
                analysis["sample_structure"] = {k: type(v).__name__ for k, v in first_item.items()}
                
                # Detect format
                if "messages" in first_item:
                    analysis["detected_format"] = "conversation_messages"
                elif "prompt" in first_item and "ai_response" in first_item:
                    analysis["detected_format"] = "prompt_response"
                elif "input" in first_item and "output" in first_item:
                    analysis["detected_format"] = "input_output"
                elif "question" in first_item and "answer" in first_item:
                    analysis["detected_format"] = "question_answer"
                
                # Collect all fields
                for item in data[:100]:  # Sample first 100 items
                    if isinstance(item, dict):
                        analysis["fields"].update(item.keys())
                        
        elif isinstance(data, dict):
            analysis["total_items"] = 1
            analysis["sample_structure"] = {k: type(v).__name__ for k, v in data.items()}
            analysis["fields"] = set(data.keys())
            
            if "messages" in data:
                analysis["detected_format"] = "single_conversation"
            elif "conversations" in data:
                analysis["detected_format"] = "conversation_collection"
                analysis["total_items"] = len(data["conversations"])
        
        analysis["fields"] = list(analysis["fields"])
        return analysis
        
    except Exception as e:
        return {
            "file_path": str(file_path),
            "error": str(e)
        }


def print_analysis(analysis: Dict[str, Any]):
    """Print analysis results in a readable format."""
    print(f"\n📁 File: {analysis['file_path']}")
    print(f"📊 Size: {analysis.get('file_size_mb', 0):.2f} MB")
    
    if "error" in analysis:
        print(f"❌ Error: {analysis['error']}")
        return
    
    print(f"📦 Data Type: {analysis['data_type']}")
    print(f"🔢 Total Items: {analysis['total_items']:,}")
    print(f"🏷️  Detected Format: {analysis['detected_format']}")
    
    if analysis['fields']:
        print(f"🔑 Fields: {', '.join(analysis['fields'])}")
    
    if analysis['sample_structure']:
        print("📋 Sample Structure:")
        for field, field_type in analysis['sample_structure'].items():
            print(f"   {field}: {field_type}")


def show_sample_data(file_path: Path, num_samples: int = 3):
    """Show sample data from the file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"\n📝 Sample Data from {file_path.name}:")
        print("=" * 60)
        
        if isinstance(data, list):
            samples = data[:num_samples]
        elif isinstance(data, dict):
            if "conversations" in data:
                samples = data["conversations"][:num_samples]
            else:
                samples = [data]
        else:
            samples = []
        
        for i, sample in enumerate(samples, 1):
            print(f"\nSample {i}:")
            print("-" * 30)
            
            if isinstance(sample, dict):
                if "messages" in sample:
                    # Conversation format
                    for msg in sample["messages"][:2]:  # Show first 2 messages
                        role = msg.get("role", "unknown")
                        content = msg.get("content", "")[:100] + "..." if len(msg.get("content", "")) > 100 else msg.get("content", "")
                        print(f"{role.capitalize()}: {content}")
                elif "prompt" in sample and "ai_response" in sample:
                    # Prompt-response format
                    prompt = sample["prompt"][:100] + "..." if len(sample["prompt"]) > 100 else sample["prompt"]
                    response = sample["ai_response"][:100] + "..." if len(sample["ai_response"]) > 100 else sample["ai_response"]
                    print(f"Prompt: {prompt}")
                    print(f"Response: {response}")
                else:
                    # Generic format
                    for key, value in list(sample.items())[:3]:  # Show first 3 fields
                        if isinstance(value, str):
                            display_value = value[:100] + "..." if len(value) > 100 else value
                        else:
                            display_value = str(value)
                        print(f"{key}: {display_value}")
            
    except Exception as e:
        print(f"❌ Error showing samples: {e}")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Inspect TSNN training data")
    parser.add_argument(
        "--data-dir",
        default="data",
        help="Directory containing data files"
    )
    parser.add_argument(
        "--show-samples",
        action="store_true",
        help="Show sample data from each file"
    )
    parser.add_argument(
        "--num-samples",
        type=int,
        default=3,
        help="Number of samples to show per file"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    data_dir = Path(args.data_dir)
    if not data_dir.exists():
        print(f"❌ Directory {data_dir} not found!")
        return
    
    # Find JSON files
    json_files = list(data_dir.rglob("*.json"))
    if not json_files:
        print(f"❌ No JSON files found in {data_dir}")
        return
    
    print(f"🔍 Found {len(json_files)} JSON files in {data_dir}")
    print("=" * 80)
    
    total_items = 0
    supported_formats = 0
    
    for json_file in json_files:
        analysis = analyze_json_structure(json_file)
        print_analysis(analysis)
        
        if "error" not in analysis:
            total_items += analysis.get("total_items", 0)
            if analysis.get("detected_format") != "unknown":
                supported_formats += 1
        
        if args.show_samples:
            show_sample_data(json_file, args.num_samples)
    
    print("\n" + "=" * 80)
    print("📊 SUMMARY")
    print("=" * 80)
    print(f"📁 Total Files: {len(json_files)}")
    print(f"✅ Supported Formats: {supported_formats}")
    print(f"🔢 Total Training Items: {total_items:,}")
    
    if supported_formats > 0:
        print(f"\n✅ Your data is compatible with TSNN training!")
        print(f"🚀 You can start training with:")
        print(f"   python train_with_data.py --config medium --max-samples 10000")
    else:
        print(f"\n⚠️  No supported formats detected.")
        print(f"📖 Supported formats:")
        print(f"   - Conversation messages: {{\"messages\": [{{\"role\": \"user\", \"content\": \"...\"}}, ...]}}")
        print(f"   - Prompt-response: {{\"prompt\": \"...\", \"ai_response\": \"...\"}}")
        print(f"   - Input-output: {{\"input\": \"...\", \"output\": \"...\"}}")


if __name__ == "__main__":
    main()
