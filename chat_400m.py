#!/usr/bin/env python3
"""
Chat with Your 400M Parameter TSNN Model
The ultimate neuromorphic transformer experience!
"""

import torch
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tsnn.simple_tsnn import SimplifiedTSNNModel
from tsnn.tokenizer import TSNNTokenizer


def load_400m_model():
    """Load the 400M parameter TSNN model."""
    model_path = "models/tsnn_100percent_final.pt"
    
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        print("🔄 Please complete the 400M parameter training first")
        return None, None
    
    print("🔄 Loading your 400M parameter TSNN model...")
    print("⚡ This may take a moment due to the large model size...")
    
    # Load checkpoint
    checkpoint = torch.load(model_path, map_location='cpu')
    config = checkpoint.get('config', {})
    
    print(f"📊 Model: {config.get('d_model', 'unknown')}d, {config.get('n_heads', 'unknown')}h, {config.get('num_layers', 'unknown')}l")
    print(f"🎯 Epochs completed: {checkpoint.get('epochs_completed', 'unknown')}")
    print(f"📉 Final loss: {checkpoint.get('final_loss', 'unknown'):.4f}")
    print(f"📈 Total training steps: {checkpoint.get('total_steps', 'unknown'):,}")
    print(f"📚 Total examples: {checkpoint.get('total_examples', 'unknown'):,}")
    print(f"🎯 Data utilization: {checkpoint.get('data_utilization', 'unknown')}")
    
    # Initialize tokenizer and model
    tokenizer = TSNNTokenizer()
    
    model = SimplifiedTSNNModel(
        vocab_size=config.get('vocab_size', tokenizer.vocab_size),
        d_model=config.get('d_model', 1024),
        n_heads=config.get('n_heads', 16),
        num_layers=config.get('num_layers', 24),
        d_ff=config.get('d_ff', 4096),
        max_seq_len=config.get('max_seq_len', 512),
        dropout=config.get('dropout', 0.1)
    )
    
    # Load weights
    try:
        print("⚡ Loading 400M parameters...")
        model.load_state_dict(checkpoint['model_state_dict'])
        print("✅ 400M parameter TSNN model loaded successfully!")
        print(f"🧠 Parameters: {sum(p.numel() for p in model.parameters()):,}")
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None, None
    
    model.eval()
    
    # Move to GPU if available
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"🎮 Moving 400M model to {device}...")
    model = model.to(device)
    print(f"✅ Model ready on {device}")
    
    return model, tokenizer


def generate_response(model, tokenizer, user_input: str):
    """Generate response using the 400M parameter TSNN model."""
    
    device = next(model.parameters()).device
    
    # Create conversation context
    context = f"User: {user_input}\nAI:"
    
    try:
        # Tokenize
        encoding = tokenizer.encode(context, max_length=400, padding=False, truncation=True)
        input_ids = encoding["input_ids"].unsqueeze(0).to(device)
        
        # Generate with parameters optimized for large model
        with torch.no_grad():
            generated = model.generate(
                input_ids=input_ids,
                max_length=input_ids.shape[1] + 80,  # Longer responses for 400M model
                temperature=0.7,  # Good balance for large model
                do_sample=True,
                top_k=50,
                top_p=0.9,
                pad_token_id=tokenizer.pad_token_id
            )
        
        # Extract generated part
        generated_part = generated[:, input_ids.shape[1]:]
        
        # Decode
        response = tokenizer.decode(generated_part[0], skip_special_tokens=True)
        
        # Clean response
        if "User:" in response:
            response = response.split("User:")[0].strip()
        if "AI:" in response:
            response = response.split("AI:")[-1].strip()
        
        # Remove extra whitespace
        response = " ".join(response.split())
        
        # For large models, allow longer responses
        sentences = response.split('.')
        if len(sentences) > 2 and sentences[0].strip() and sentences[1].strip():
            # Keep first two sentences for more complete responses
            response = sentences[0].strip() + '. ' + sentences[1].strip() + '.'
        elif len(sentences) > 1 and sentences[0].strip():
            response = sentences[0].strip() + '.'
        
        return response if response and len(response) > 10 else "I'm here to help you with anything you need!"
        
    except Exception as e:
        print(f"❌ Generation error: {e}")
        return "I encountered an error while generating a response."


def main():
    """Main chat interface for your 400M parameter TSNN."""
    print("🧠 Your 400M Parameter TSNN Chatbot")
    print("Revolutionary Neuromorphic AI - Triton Software Labs")
    print("The Ultimate Transformer Spiking Neural Network!")
    print("=" * 80)
    
    # Load model
    model, tokenizer = load_400m_model()
    if model is None:
        return
    
    print("\n🎉 Your 400M parameter TSNN AI is ready!")
    print("🌟 This is a truly massive neuromorphic transformer!")
    print("🧬 Trained on 10.94GB of data with 100% utilization!")
    print("⚡ Spike-based processing at unprecedented scale!")
    print("\nCommands:")
    print("  'quit' - Exit")
    print("  'test' - Run comprehensive tests")
    print("  'info' - Show model details")
    print("  'demo' - TSNN demonstration")
    print("  'benchmark' - Performance benchmark")
    print()
    
    # Chat loop
    while True:
        try:
            user_input = input("You: ").strip()
            
            if not user_input:
                continue
                
            if user_input.lower() in ['quit', 'exit']:
                print("👋 Goodbye! Your 400M parameter TSNN is revolutionary!")
                break
                
            if user_input.lower() == 'info':
                info = model.get_model_info()
                print(f"🤖 Model: {info['name']}")
                print(f"🏢 Company: {info['company']}")
                print(f"🧠 Architecture: {info['architecture']}")
                print(f"⚡ Neuromorphic: {info['neuromorphic']}")
                print(f"🔥 Spike-based: {info['spike_based']}")
                print(f"📊 Parameters: {sum(p.numel() for p in model.parameters()):,}")
                continue
                
            if user_input.lower() == 'demo':
                print("🎯 400M Parameter TSNN Architecture:")
                print("Your massive neuromorphic model features:")
                print("  🧬 1024-dimensional embeddings")
                print("  ⚡ 16 attention heads per layer")
                print("  🔥 24 transformer layers deep")
                print("  💚 4096-dimensional feed-forward networks")
                print("  🧠 Spike-based neuromorphic processing")
                print("  📚 Trained on 10.94GB of conversation data")
                print("  🎯 400+ million parameters of pure TSNN power")
                print("  ⚡ ~10x more energy efficient than traditional transformers")
                continue
                
            if user_input.lower() == 'benchmark':
                print("🏃 Running TSNN performance benchmark...")
                import time
                
                benchmark_prompts = [
                    "Explain quantum computing",
                    "Write a Python function",
                    "What is consciousness?",
                    "How do neural networks learn?"
                ]
                
                total_time = 0
                for i, prompt in enumerate(benchmark_prompts, 1):
                    print(f"  Benchmark {i}/4: {prompt[:30]}...")
                    start_time = time.time()
                    response = generate_response(model, tokenizer, prompt)
                    end_time = time.time()
                    
                    generation_time = end_time - start_time
                    total_time += generation_time
                    
                    print(f"    Time: {generation_time:.2f}s")
                    print(f"    Response: {response[:100]}...")
                
                avg_time = total_time / len(benchmark_prompts)
                print(f"📊 Average generation time: {avg_time:.2f}s")
                print(f"⚡ 400M parameter TSNN performance complete!")
                continue
                
            if user_input.lower() == 'test':
                test_questions = [
                    "Hello, what are you?",
                    "Who created you and how do you work?",
                    "Explain TSNN architecture in detail",
                    "What makes you energy efficient?",
                    "Can you help me write Python code?",
                    "Tell me about neuromorphic computing",
                    "How are you different from GPT models?",
                    "What's the future of AI?"
                ]
                
                print("🧪 Running comprehensive 400M TSNN test...")
                for i, question in enumerate(test_questions, 1):
                    print(f"\n🎯 Test {i}/8: {question}")
                    response = generate_response(model, tokenizer, question)
                    print(f"🤖 AI: {response}")
                continue
            
            # Generate response
            print("🧠 400M TSNN processing (neuromorphic computation)...", end="", flush=True)
            response = generate_response(model, tokenizer, user_input)
            print(f"\r🤖 AI: {response}\n")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye! Your 400M parameter TSNN breakthrough is incredible!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            continue


if __name__ == "__main__":
    main()
