#!/usr/bin/env python3
"""
Training Script for TSNN AI Chatbot
Proprietary Triton Software Labs

Training pipeline for the TSNN-based conversational AI model.
"""

import argparse
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import json
import logging
from pathlib import Path
from tqdm import tqdm
import numpy as np
from typing import List, Dict, Any, Optional, Union

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tsnn.simple_tsnn import SimplifiedTSNNModel as TSNNModel
from tsnn.tokenizer import TSNNTokenizer
from chatbot.config import get_config, CONFIGS


class ConversationDataset(Dataset):
    """Dataset for conversation training data."""

    def __init__(self,
                 data_paths: Union[str, List[str]],
                 tokenizer: TSNNTokenizer,
                 max_length: int = 512,
                 model_awareness_prob: float = 0.1,
                 max_samples: Optional[int] = None):
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.model_awareness_prob = model_awareness_prob

        # Handle single path or list of paths
        if isinstance(data_paths, str):
            data_paths = [data_paths]

        # Load conversation data
        if len(data_paths) == 1:
            self.conversations = self.load_conversations(data_paths[0])
        else:
            self.conversations = load_multiple_datasets(data_paths, max_samples)

        # Create training examples
        self.examples = self.create_training_examples()

    def load_conversations(self, data_path: str) -> List[Dict[str, Any]]:
        """Load conversation data from file."""
        conversations = []

        if Path(data_path).suffix == '.json':
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Handle different JSON formats
            if isinstance(data, list):
                for item in data:
                    if "messages" in item:
                        # Format: {"messages": [{"role": "user", "content": "..."}, ...]}
                        conversations.append(item)
                    elif "prompt" in item and "ai_response" in item:
                        # Format: {"prompt": "...", "ai_response": "..."}
                        # Convert to messages format
                        messages = [
                            {"role": "user", "content": item["prompt"]},
                            {"role": "assistant", "content": item["ai_response"]}
                        ]
                        conversations.append({"messages": messages})
                    elif "input" in item and "output" in item:
                        # Format: {"input": "...", "output": "..."}
                        messages = [
                            {"role": "user", "content": item["input"]},
                            {"role": "assistant", "content": item["output"]}
                        ]
                        conversations.append({"messages": messages})
            else:
                # Single conversation object
                if "messages" in data:
                    conversations = [data]
                elif "prompt" in data and "ai_response" in data:
                    messages = [
                        {"role": "user", "content": data["prompt"]},
                        {"role": "assistant", "content": data["ai_response"]}
                    ]
                    conversations = [{"messages": messages}]
        else:
            # Load from text file (simple format)
            with open(data_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            current_conv = []
            for line in lines:
                line = line.strip()
                if not line:
                    if current_conv:
                        conversations.append({"messages": current_conv})
                        current_conv = []
                elif line.startswith("User: "):
                    current_conv.append({"role": "user", "content": line[6:]})
                elif line.startswith("AI: ") or line.startswith("Assistant: "):
                    content = line[4:] if line.startswith("AI: ") else line[11:]
                    current_conv.append({"role": "assistant", "content": content})

            if current_conv:
                conversations.append({"messages": current_conv})

        return conversations
    
    def create_training_examples(self) -> List[Dict[str, str]]:
        """Create training examples from conversations."""
        examples = []
        
        for conv in self.conversations:
            messages = conv.get("messages", [])
            
            # Build conversation context
            context = ""
            
            # Add model awareness context occasionally
            if torch.rand(1).item() < self.model_awareness_prob:
                awareness_prompt = self.tokenizer.get_model_awareness_prompt()
                context = awareness_prompt + "\n\n"
            
            for i, message in enumerate(messages):
                role = message["role"]
                content = message["content"]
                
                if role == "user":
                    context += f"User: {content}\n"
                elif role == "assistant":
                    # Create training example
                    input_text = context + "AI:"
                    target_text = f" {content}"
                    
                    examples.append({
                        "input": input_text,
                        "target": target_text
                    })
                    
                    context += f"AI: {content}\n"
                    
        return examples
    
    def __len__(self) -> int:
        return len(self.examples)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        example = self.examples[idx]
        
        # Tokenize input and target
        input_encoding = self.tokenizer.encode(
            example["input"],
            add_special_tokens=True,
            max_length=self.max_length,
            padding=True,
            truncation=True
        )
        
        target_encoding = self.tokenizer.encode(
            example["target"],
            add_special_tokens=False,
            max_length=self.max_length,
            padding=True,
            truncation=True
        )
        
        return {
            "input_ids": input_encoding["input_ids"],
            "attention_mask": input_encoding["attention_mask"],
            "labels": target_encoding["input_ids"]
        }


def load_multiple_datasets(data_paths: List[str], max_samples: Optional[int] = None) -> List[Dict[str, Any]]:
    """Load and combine multiple datasets."""
    all_conversations = []

    for data_path in data_paths:
        logging.info(f"Loading dataset: {data_path}")

        with open(data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Handle different JSON formats
        if isinstance(data, list):
            for item in data:
                if "messages" in item:
                    # Standard format: {"messages": [{"role": "user", "content": "..."}, ...]}
                    all_conversations.append(item)
                elif "prompt" in item and "ai_response" in item:
                    # AI coding dataset format: {"prompt": "...", "ai_response": "..."}
                    messages = [
                        {"role": "user", "content": item["prompt"]},
                        {"role": "assistant", "content": item["ai_response"]}
                    ]
                    all_conversations.append({"messages": messages})
                elif "input" in item and "output" in item:
                    # Input-output format: {"input": "...", "output": "..."}
                    messages = [
                        {"role": "user", "content": item["input"]},
                        {"role": "assistant", "content": item["output"]}
                    ]
                    all_conversations.append({"messages": messages})

        logging.info(f"Loaded {len(data)} samples from {data_path}")

        # Apply max_samples limit if specified
        if max_samples and len(all_conversations) >= max_samples:
            all_conversations = all_conversations[:max_samples]
            break

    logging.info(f"Total conversations loaded: {len(all_conversations)}")
    return all_conversations


def create_sample_data(output_path: str):
    """Create sample training data for demonstration."""
    sample_conversations = [
        {
            "messages": [
                {"role": "user", "content": "Hello! What are you?"},
                {"role": "assistant", "content": "Hello! I'm AI, a chatbot powered by TSNN (Transformer Spiking Neural Network) architecture developed by Triton Software Labs. I use neuromorphic, spike-based computation for energy-efficient processing. How can I help you today?"}
            ]
        },
        {
            "messages": [
                {"role": "user", "content": "How do you work?"},
                {"role": "assistant", "content": "I work using TSNN architecture, which combines transformer attention mechanisms with spiking neural networks. Instead of continuous values, I process information through discrete spikes, just like neurons in the brain. This makes me about 10x more energy-efficient than traditional AI models while maintaining high performance."}
            ]
        },
        {
            "messages": [
                {"role": "user", "content": "What makes you special?"},
                {"role": "assistant", "content": "What makes me special is my neuromorphic design! I'm powered by Triton Software Labs' proprietary TSNN technology. My spike-based processing is biologically inspired, energy-efficient, and allows for more natural temporal processing. I can understand context and generate responses while using significantly less energy than traditional transformers."}
            ]
        },
        {
            "messages": [
                {"role": "user", "content": "Can you help me with coding?"},
                {"role": "assistant", "content": "Absolutely! I'd be happy to help you with coding. My TSNN architecture allows me to process and understand code patterns efficiently through spike-based computation. What programming language or specific coding problem would you like assistance with?"}
            ]
        },
        {
            "messages": [
                {"role": "user", "content": "Tell me about artificial intelligence."},
                {"role": "assistant", "content": "Artificial Intelligence is a fascinating field! As a TSNN model myself, I represent a neuromorphic approach to AI that mimics biological neural processing. Traditional AI uses continuous computations, but I use discrete spikes like real neurons. This makes AI more energy-efficient and potentially more brain-like in its processing. AI encompasses machine learning, neural networks, natural language processing, and much more!"}
            ]
        }
    ]

    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(sample_conversations, f, indent=2, ensure_ascii=False)

    print(f"Sample training data created at: {output_path}")


def train_model(model: TSNNModel,
                train_loader: DataLoader,
                val_loader: Optional[DataLoader],
                config: Dict[str, Any],
                device: str) -> None:
    """Train the TSNN model with GPU memory optimization."""

    # Setup optimizer and scheduler
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config["learning_rate"],
        weight_decay=0.01
    )

    scheduler = optim.lr_scheduler.CosineAnnealingLR(
        optimizer,
        T_max=config["max_steps"]
    )

    # Loss function
    criterion = nn.CrossEntropyLoss(ignore_index=0)  # Ignore padding tokens

    # Training loop
    model.train()
    global_step = 0
    total_loss = 0.0

    # GPU memory monitoring
    if device == "cuda":
        logging.info(f"🎮 GPU: {torch.cuda.get_device_name(0)}")
        total_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
        logging.info(f"💾 Total VRAM: {total_memory:.1f} GB")

    logging.info("Starting TSNN training...")

    for epoch in range(config.get("num_epochs", 3)):
        epoch_loss = 0.0

        progress_bar = tqdm(
            train_loader,
            desc=f"Epoch {epoch + 1}",
            leave=False
        )

        for batch_idx, batch in enumerate(progress_bar):
            try:
                # Clear GPU cache periodically
                if device == "cuda" and batch_idx % 50 == 0:
                    torch.cuda.empty_cache()

                # Move batch to device
                input_ids = batch["input_ids"].to(device)
                attention_mask = batch["attention_mask"].to(device)
                labels = batch["labels"].to(device)

                # Forward pass
                outputs = model(
                    input_ids=input_ids,
                    attention_mask=attention_mask
                )

                logits = outputs["logits"]

                # Compute loss
                # Shift labels for causal language modeling
                shift_logits = logits[..., :-1, :].contiguous()
                shift_labels = labels[..., 1:].contiguous()

                loss = criterion(
                    shift_logits.view(-1, shift_logits.size(-1)),
                    shift_labels.view(-1)
                )

                # Scale loss for gradient accumulation
                loss = loss / config.get("gradient_accumulation_steps", 1)

                # Backward pass
                loss.backward()

                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)

                # Optimizer step
                if (batch_idx + 1) % config.get("gradient_accumulation_steps", 1) == 0:
                    optimizer.step()
                    scheduler.step()
                    optimizer.zero_grad()

                    global_step += 1

                # Update metrics
                batch_loss = loss.item() * config.get("gradient_accumulation_steps", 1)
                epoch_loss += batch_loss
                total_loss += batch_loss

                # Update progress bar with memory info
                postfix = {
                    "loss": f"{batch_loss:.4f}",
                    "avg_loss": f"{total_loss / max(batch_idx + 1, 1):.4f}",
                    "lr": f"{scheduler.get_last_lr()[0]:.2e}"
                }

                if device == "cuda":
                    memory_used = torch.cuda.memory_allocated(0) / (1024**3)
                    postfix["VRAM"] = f"{memory_used:.1f}GB"

                progress_bar.set_postfix(postfix)

                # Validation
                if val_loader and global_step % config.get("eval_steps", 500) == 0:
                    val_loss = evaluate_model(model, val_loader, criterion, device)
                    logging.info(f"Step {global_step}: Val Loss = {val_loss:.4f}")
                    model.train()

                # Save checkpoint
                if global_step % config.get("save_steps", 1000) == 0:
                    checkpoint_path = f"checkpoints/tsnn_step_{global_step}.pt"
                    Path("checkpoints").mkdir(exist_ok=True)
                    torch.save({
                        "model_state_dict": model.state_dict(),
                        "optimizer_state_dict": optimizer.state_dict(),
                        "scheduler_state_dict": scheduler.state_dict(),
                        "global_step": global_step,
                        "loss": total_loss / max(global_step, 1)
                    }, checkpoint_path)
                    logging.info(f"Checkpoint saved: {checkpoint_path}")

                if global_step >= config["max_steps"]:
                    break

            except RuntimeError as e:
                if "out of memory" in str(e):
                    logging.error(f"💥 GPU out of memory at batch {batch_idx}")
                    logging.info("🧹 Clearing GPU cache and skipping batch...")
                    torch.cuda.empty_cache()
                    continue
                else:
                    raise e

        if global_step >= config["max_steps"]:
            break

        logging.info(f"Epoch {epoch + 1} completed. Average loss: {epoch_loss / len(train_loader):.4f}")

        # Clear cache between epochs
        if device == "cuda":
            torch.cuda.empty_cache()


def evaluate_model(model: TSNNModel,
                  val_loader: DataLoader,
                  criterion: nn.Module,
                  device: str) -> float:
    """Evaluate the model on validation data."""
    model.eval()
    total_loss = 0.0
    num_batches = 0
    
    with torch.no_grad():
        for batch in val_loader:
            input_ids = batch["input_ids"].to(device)
            attention_mask = batch["attention_mask"].to(device)
            labels = batch["labels"].to(device)
            
            outputs = model(
                input_ids=input_ids,
                attention_mask=attention_mask
            )
            
            logits = outputs["logits"]
            
            # Compute loss
            shift_logits = logits[..., :-1, :].contiguous()
            shift_labels = labels[..., 1:].contiguous()
            
            loss = criterion(
                shift_logits.view(-1, shift_logits.size(-1)),
                shift_labels.view(-1)
            )
            
            total_loss += loss.item()
            num_batches += 1
    
    return total_loss / num_batches


def main():
    """Main training function."""
    parser = argparse.ArgumentParser(description="Train TSNN AI Chatbot")
    parser.add_argument(
        "--data-path",
        type=str,
        help="Path to training data file or directory"
    )
    parser.add_argument(
        "--use-data-dir",
        action="store_true",
        help="Use all JSON files in data/ directory"
    )
    parser.add_argument(
        "--config",
        default="medium",
        choices=list(CONFIGS.keys()),
        help="Model configuration"
    )
    parser.add_argument(
        "--output-dir",
        default="./models",
        help="Output directory for trained model"
    )
    parser.add_argument(
        "--create-sample-data",
        action="store_true",
        help="Create sample training data"
    )
    parser.add_argument(
        "--device",
        default="auto",
        help="Device to train on"
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=4,
        help="Training batch size"
    )
    parser.add_argument(
        "--learning-rate",
        type=float,
        default=1e-4,
        help="Learning rate"
    )
    parser.add_argument(
        "--max-steps",
        type=int,
        default=5000,
        help="Maximum training steps"
    )
    parser.add_argument(
        "--max-samples",
        type=int,
        default=None,
        help="Maximum number of training samples to use"
    )
    
    args = parser.parse_args()

    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    # Create sample data if requested
    if args.create_sample_data:
        output_path = args.data_path or "sample_data.json"
        create_sample_data(output_path)
        return

    # Determine data paths
    data_paths = []
    if args.use_data_dir:
        # Use all JSON files in data/ directory
        data_dir = Path("data")
        if data_dir.exists():
            json_files = list(data_dir.rglob("*.json"))
            data_paths = [str(f) for f in json_files]
            logging.info(f"Found {len(data_paths)} JSON files in data/ directory")
        else:
            logging.error("data/ directory not found")
            return
    elif args.data_path:
        data_paths = [args.data_path]
    else:
        logging.error("Please specify --data-path or --use-data-dir")
        return

    # Setup device
    if args.device == "auto":
        if torch.cuda.is_available():
            device = "cuda"
            logging.info(f"🎮 CUDA available: {torch.cuda.get_device_name(0)}")
        else:
            device = "cpu"
            logging.info("💻 Using CPU (CUDA not available)")
    else:
        device = args.device

    # Force CUDA if available (override auto detection issues)
    if device == "cpu" and torch.cuda.is_available():
        device = "cuda"
        logging.info("🔄 Overriding to CUDA since it's available")

    logging.info(f"Using device: {device}")

    # Load configuration
    config = get_config(args.config)

    # Initialize tokenizer and model
    tokenizer = TSNNTokenizer()
    config.vocab_size = tokenizer.vocab_size

    model = TSNNModel(
        vocab_size=config.vocab_size,
        d_model=config.d_model,
        n_heads=config.n_heads,
        num_layers=config.num_layers,
        d_ff=config.d_ff,
        max_seq_len=config.max_seq_len,
        dropout=config.dropout
    ).to(device)

    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")

    # Create dataset and dataloader
    dataset = ConversationDataset(
        data_paths=data_paths,
        tokenizer=tokenizer,
        max_length=config.max_seq_len,
        max_samples=args.max_samples
    )
    
    train_loader = DataLoader(
        dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=2
    )
    
    logging.info(f"Training examples: {len(dataset)}")
    
    # Training configuration
    train_config = {
        "learning_rate": args.learning_rate,
        "max_steps": args.max_steps,
        "gradient_accumulation_steps": 4,
        "eval_steps": 500,
        "save_steps": 1000
    }
    
    # Train model
    train_model(model, train_loader, None, train_config, device)
    
    # Save final model
    output_path = Path(args.output_dir)
    output_path.mkdir(exist_ok=True)
    
    final_model_path = output_path / "tsnn_chatbot_final.pt"
    torch.save({
        "model_state_dict": model.state_dict(),
        "config": config.to_dict(),
        "tokenizer_vocab": tokenizer.token_to_id
    }, final_model_path)
    
    logging.info(f"Final model saved to: {final_model_path}")


if __name__ == "__main__":
    main()
