#!/usr/bin/env python3
"""
Quick Training Script for TSNN AI Chatbot with Your Data
Proprietary Triton Software Labs

Easy script to train the TSNN chatbot with the datasets in your data/ directory.
"""

import argparse
import logging
from pathlib import Path
import subprocess
import sys


def main():
    """Main function to train with your data."""
    parser = argparse.ArgumentParser(description="Train TSNN AI Chatbot with your data")
    parser.add_argument(
        "--config",
        default="medium",
        choices=["small", "medium", "large", "energy_efficient", "high_performance"],
        help="Model configuration"
    )
    parser.add_argument(
        "--max-samples",
        type=int,
        default=8000,
        help="Maximum number of training samples (GTX 1060 optimized: 5000-10000)"
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=1,
        help="Training batch size (GTX 1060 optimized: 1-2)"
    )
    parser.add_argument(
        "--max-steps",
        type=int,
        default=2000,
        help="Maximum training steps (GTX 1060 optimized)"
    )
    parser.add_argument(
        "--learning-rate",
        type=float,
        default=3e-5,
        help="Learning rate (GTX 1060 optimized)"
    )
    parser.add_argument(
        "--gradient-accumulation",
        type=int,
        default=4,
        help="Gradient accumulation steps (helps with small batch sizes)"
    )
    parser.add_argument(
        "--device",
        default="auto",
        help="Device to train on (auto/cpu/cuda)"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Check if data directory exists
    data_dir = Path("data")
    if not data_dir.exists():
        logging.error("data/ directory not found!")
        logging.info("Please make sure you have a data/ directory with JSON files.")
        return
    
    # Find JSON files
    json_files = list(data_dir.rglob("*.json"))
    if not json_files:
        logging.error("No JSON files found in data/ directory!")
        return
    
    logging.info(f"Found {len(json_files)} JSON files:")
    for file in json_files:
        logging.info(f"  - {file}")
    
    # Create models directory
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    
    # Check if CUDA is available and warn about memory
    if args.device == "auto" or args.device == "cuda":
        try:
            import torch
            if torch.cuda.is_available():
                gpu_name = torch.cuda.get_device_name(0)
                total_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                logging.info(f"🎮 Detected GPU: {gpu_name}")
                logging.info(f"💾 VRAM: {total_memory:.1f} GB")

                # GTX 1060 specific optimizations
                if "1060" in gpu_name:
                    logging.info("🔧 GTX 1060 detected! Using optimized settings...")
                    if args.batch_size > 2:
                        logging.warning("⚠️  Reducing batch size to 2 for GTX 1060")
                        args.batch_size = 2
                    if args.max_samples > 10000:
                        logging.warning("⚠️  Reducing max samples to 10000 for GTX 1060")
                        args.max_samples = 10000
        except ImportError:
            pass

    # Build training command
    cmd = [
        sys.executable, "train.py",
        "--use-data-dir",
        "--config", args.config,
        "--max-samples", str(args.max_samples),
        "--batch-size", str(args.batch_size),
        "--max-steps", str(args.max_steps),
        "--learning-rate", str(args.learning_rate),
        "--device", args.device,
        "--output-dir", "models"
    ]
    
    logging.info("Starting training with command:")
    logging.info(" ".join(cmd))
    
    try:
        # Run training
        result = subprocess.run(cmd, check=True)
        logging.info("Training completed successfully!")
        
        # Check if model was created
        model_files = list(models_dir.glob("*.pt"))
        if model_files:
            logging.info("Model files created:")
            for model_file in model_files:
                logging.info(f"  - {model_file}")
            
            logging.info("\nYou can now chat with your trained model:")
            logging.info(f"python chat.py --model-path {model_files[-1]} --config {args.config}")
        
    except subprocess.CalledProcessError as e:
        logging.error(f"Training failed with error: {e}")
        return
    except KeyboardInterrupt:
        logging.info("Training interrupted by user")
        return


if __name__ == "__main__":
    main()
