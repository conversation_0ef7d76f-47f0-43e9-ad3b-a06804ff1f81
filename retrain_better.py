#!/usr/bin/env python3
"""
Retrain TSNN with Better Data Formatting
Fix the EOS issue by improving training data format
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import json
import logging
from pathlib import Path
from tqdm import tqdm
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tsnn.simple_tsnn import SimplifiedTSNNModel
from tsnn.tokenizer import TSNNTokenizer


class BetterConversationDataset(Dataset):
    """Better formatted dataset to fix EOS issue."""
    
    def __init__(self, max_length: int = 256, max_samples: int = 10000):
        self.tokenizer = TSNNTokenizer()
        self.max_length = max_length
        self.max_samples = max_samples
        
        # Load data with better formatting
        self.examples = self.load_better_data()
        
        logging.info(f"Created {len(self.examples)} better formatted examples")
    
    def load_better_data(self) -> list:
        """Load data with improved formatting."""
        examples = []
        
        # Load ShareGPT data
        sharegpt_path = "data/Chatbot-Datasets-main/sharegpt_clean.json"
        if os.path.exists(sharegpt_path):
            logging.info("Loading ShareGPT with better formatting...")
            with open(sharegpt_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            count = 0
            for conversation in data:
                if count >= self.max_samples:
                    break
                    
                if "items" not in conversation:
                    continue
                    
                items = conversation["items"]
                
                for i in range(len(items) - 1):
                    if (items[i]["from"] == "human" and 
                        items[i + 1]["from"] == "gpt"):
                        
                        user_msg = items[i]["value"].strip()
                        ai_msg = items[i + 1]["value"].strip()
                        
                        # Better quality filter
                        if (10 < len(user_msg) < 200 and 
                            10 < len(ai_msg) < 300 and
                            len(user_msg.split()) < 50 and 
                            len(ai_msg.split()) < 80):
                            
                            examples.append({
                                "input": user_msg,
                                "output": ai_msg
                            })
                            count += 1
                            
                            if count >= self.max_samples:
                                break
            
            logging.info(f"Loaded {count} examples from ShareGPT")
        
        # Add high-quality TSNN examples
        tsnn_examples = [
            {
                "input": "What are you?",
                "output": "I am AI, a chatbot powered by TSNN architecture from Triton Software Labs. I use neuromorphic spike-based processing for energy-efficient conversations."
            },
            {
                "input": "Who created you?",
                "output": "I was created by Triton Software Labs using TSNN technology. TSNN stands for Transformer Spiking Neural Network, which makes me energy-efficient."
            },
            {
                "input": "How do you work?",
                "output": "I work using TSNN architecture that combines transformers with spiking neural networks. This allows me to process information like biological neurons while maintaining high performance."
            },
            {
                "input": "Hello",
                "output": "Hello! I'm AI, your neuromorphic assistant. I use TSNN technology for efficient and brain-like processing. How can I help you today?"
            },
            {
                "input": "Hi there",
                "output": "Hi! I'm an AI chatbot built with TSNN architecture by Triton Software Labs. I'm designed to be helpful and energy-efficient. What would you like to know?"
            }
        ]
        
        # Add TSNN examples with high weight
        examples.extend(tsnn_examples * 100)  # 100x weight
        
        return examples[:self.max_samples]
    
    def __len__(self):
        return len(self.examples)
    
    def __getitem__(self, idx):
        example = self.examples[idx]
        
        # BETTER FORMAT: Don't use EOS in the middle, only at the very end
        full_text = f"User: {example['input']}\nAI: {example['output']}"
        
        # Tokenize the full conversation
        encoding = self.tokenizer.encode(
            full_text, 
            max_length=self.max_length, 
            padding=True, 
            truncation=True,
            add_special_tokens=True
        )
        
        input_ids = encoding["input_ids"]
        attention_mask = encoding["attention_mask"]
        
        # Create labels for causal language modeling
        labels = input_ids.clone()
        
        # Find where "AI:" starts to only compute loss on AI response
        full_text_tokens = self.tokenizer.encode(f"User: {example['input']}\nAI:", add_special_tokens=True)["input_ids"]
        ai_start = len(full_text_tokens) - 1  # -1 because we want to predict the space after "AI:"
        
        # Mask out the user input in the loss
        labels[:ai_start] = -100
        
        return {
            "input_ids": input_ids,
            "attention_mask": attention_mask,
            "labels": labels
        }


def retrain_better():
    """Retrain with better formatting."""
    
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    logging.info(f"Using device: {device}")
    
    # Create better dataset
    dataset = BetterConversationDataset(max_length=256, max_samples=15000)
    train_loader = DataLoader(dataset, batch_size=4, shuffle=True, num_workers=0)
    
    # Initialize model
    tokenizer = TSNNTokenizer()
    model = SimplifiedTSNNModel(
        vocab_size=tokenizer.vocab_size,
        d_model=384,  # Smaller for faster training
        n_heads=6,
        num_layers=6,
        d_ff=1536,
        max_seq_len=256,
        dropout=0.1
    ).to(device)
    
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Training setup
    optimizer = optim.AdamW(model.parameters(), lr=5e-5, weight_decay=0.01)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=len(train_loader) * 3)
    criterion = nn.CrossEntropyLoss(ignore_index=-100)
    
    # Train for 3 epochs
    model.train()
    total_loss = 0.0
    step = 0
    
    logging.info("Starting BETTER TSNN training...")
    
    for epoch in range(3):
        epoch_loss = 0.0
        
        progress_bar = tqdm(train_loader, desc=f"Epoch {epoch + 1}/3")
        
        for batch in progress_bar:
            input_ids = batch["input_ids"].to(device)
            attention_mask = batch["attention_mask"].to(device)
            labels = batch["labels"].to(device)
            
            # Forward pass
            outputs = model(input_ids, attention_mask)
            logits = outputs["logits"]
            
            # Compute loss
            loss = criterion(logits.view(-1, logits.size(-1)), labels.view(-1))
            
            # Backward pass
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            
            optimizer.step()
            scheduler.step()
            optimizer.zero_grad()
            
            # Update metrics
            step_loss = loss.item()
            total_loss += step_loss
            epoch_loss += step_loss
            step += 1
            
            progress_bar.set_postfix({
                "loss": f"{step_loss:.4f}",
                "avg_loss": f"{total_loss / step:.4f}",
                "lr": f"{scheduler.get_last_lr()[0]:.2e}"
            })
            
            if device == "cuda" and step % 100 == 0:
                torch.cuda.empty_cache()
        
        logging.info(f"Epoch {epoch + 1} completed. Average loss: {epoch_loss / len(train_loader):.4f}")
    
    # Save better model
    output_path = Path("models") / "tsnn_better_final.pt"
    torch.save({
        "model_state_dict": model.state_dict(),
        "config": {
            "vocab_size": tokenizer.vocab_size,
            "d_model": 384,
            "n_heads": 6,
            "num_layers": 6,
            "d_ff": 1536,
            "max_seq_len": 256,
            "dropout": 0.1
        },
        "epochs_completed": 3,
        "final_loss": total_loss / step
    }, output_path)
    
    logging.info(f"🎉 Better TSNN model saved to: {output_path}")
    logging.info("This model should generate proper responses!")


if __name__ == "__main__":
    retrain_better()
