#!/usr/bin/env python3
"""
Simple Chat Interface for TSNN Model
Quick test of the trained TSNN chatbot
"""

import torch
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tsnn.simple_tsnn import SimplifiedTSNNModel
from tsnn.tokenizer import TSNNTokenizer


def load_model(model_path: str, config_name: str = "small"):
    """Load the trained TSNN model."""
    print(f"🔄 Loading TSNN model from {model_path}...")
    
    # Initialize tokenizer
    tokenizer = TSNNTokenizer()
    
    # Load checkpoint to get exact configuration
    checkpoint = torch.load(model_path, map_location='cpu')
    saved_config = checkpoint.get('config', {})

    # Use saved configuration or defaults
    model_config = {
        "vocab_size": saved_config.get('vocab_size', tokenizer.vocab_size),
        "d_model": saved_config.get('d_model', 256),
        "n_heads": saved_config.get('n_heads', 4),
        "num_layers": saved_config.get('num_layers', 4),
        "d_ff": saved_config.get('d_ff', 1024),
        "max_seq_len": saved_config.get('max_seq_len', 256),
        "dropout": saved_config.get('dropout', 0.1)
    }

    print(f"📊 Model config: {model_config['d_model']}d, {model_config['n_heads']}h, {model_config['num_layers']}l")

    # Initialize model with exact saved configuration
    model = SimplifiedTSNNModel(
        vocab_size=model_config["vocab_size"],
        d_model=model_config["d_model"],
        n_heads=model_config["n_heads"],
        num_layers=model_config["num_layers"],
        d_ff=model_config["d_ff"],
        max_seq_len=model_config["max_seq_len"],
        dropout=model_config["dropout"]
    )
    
    # Load weights (checkpoint already loaded above)
    try:
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
        print("✅ Model weights loaded successfully!")
    except Exception as e:
        print(f"❌ Error loading model weights: {e}")
        print("🔄 Using randomly initialized model...")
    
    model.eval()
    return model, tokenizer


def generate_response(model, tokenizer, user_input: str, max_length: int = 50):
    """Generate a response using the TSNN model."""
    
    # Create conversation context
    context = f"User: {user_input}\nAI:"
    
    # Tokenize
    try:
        encoding = tokenizer.encode(context, max_length=100, padding=False, truncation=True)
        input_ids = encoding["input_ids"].unsqueeze(0)
        
        # Generate
        with torch.no_grad():
            generated = model.generate(
                input_ids=input_ids,
                max_length=input_ids.shape[1] + max_length,
                temperature=0.8,
                do_sample=True,
                top_k=50,
                pad_token_id=tokenizer.pad_token_id
            )
        
        # Extract only the generated part
        generated_part = generated[:, input_ids.shape[1]:]
        
        # Decode
        response = tokenizer.decode(generated_part[0], skip_special_tokens=True)
        
        # Clean up response
        if "User:" in response:
            response = response.split("User:")[0].strip()
        if "AI:" in response:
            response = response.split("AI:")[-1].strip()
            
        return response if response else "I'm still learning to respond properly with my TSNN architecture!"
        
    except Exception as e:
        print(f"❌ Generation error: {e}")
        return "I apologize, but I encountered an error while processing your message. As a TSNN model, I'm still learning to handle all types of inputs efficiently."


def main():
    """Main chat loop."""
    print("🧠 Simple TSNN Chat Interface")
    print("Proprietary Triton Software Labs")
    print("=" * 50)
    
    # Load model
    model_path = "models/tsnn_chatbot_final.pt"
    if not os.path.exists(model_path):
        print(f"❌ Model file not found: {model_path}")
        print("🔄 Please train the model first with: python train_with_data.py")
        return
    
    model, tokenizer = load_model(model_path, "small")
    
    print("✅ TSNN AI is ready to chat!")
    print("Type 'quit' to exit, 'help' for commands\n")
    
    # Chat loop
    while True:
        try:
            user_input = input("You: ").strip()
            
            if not user_input:
                continue
                
            if user_input.lower() in ['quit', 'exit']:
                print("👋 Goodbye! Thanks for testing the TSNN architecture!")
                break
                
            if user_input.lower() == 'help':
                print("Commands:")
                print("  quit/exit - Exit the chat")
                print("  help - Show this help")
                print("  info - Show model information")
                continue
                
            if user_input.lower() == 'info':
                info = model.get_model_info()
                print(f"🤖 Model: {info['name']}")
                print(f"🏢 Company: {info['company']}")
                print(f"🧠 Architecture: {info['architecture']}")
                print(f"⚡ Neuromorphic: {info['neuromorphic']}")
                print(f"🔥 Spike-based: {info['spike_based']}")
                continue
            
            # Generate response
            print("🧠 AI is thinking...", end="", flush=True)
            response = generate_response(model, tokenizer, user_input)
            print(f"\r🤖 AI: {response}\n")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye! Thanks for testing the TSNN architecture!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            continue


if __name__ == "__main__":
    main()
