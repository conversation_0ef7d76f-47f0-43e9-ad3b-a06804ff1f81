#!/usr/bin/env python3
"""
GTX 1060 Optimized Training Script for TSNN AI Chatbot
Proprietary Triton Software Labs

Optimized training configuration for NVIDIA GTX 1060 (6GB VRAM)
with incremental training to avoid memory overload.
"""

import argparse
import logging
import torch
from pathlib import Path
import subprocess
import sys
import time


def check_gpu_memory():
    """Check available GPU memory."""
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
        logging.info(f"🎮 GPU: {torch.cuda.get_device_name(0)}")
        logging.info(f"💾 Total VRAM: {gpu_memory:.1f} GB")
        
        # Check current memory usage
        torch.cuda.empty_cache()
        allocated = torch.cuda.memory_allocated(0) / (1024**3)
        cached = torch.cuda.memory_reserved(0) / (1024**3)
        free = gpu_memory - cached
        
        logging.info(f"🔄 Available VRAM: {free:.1f} GB")
        return free > 1.0  # Need at least 1GB free
    return False


def train_incrementally(config: str, total_samples: int, batch_size: int = 1):
    """Train the model incrementally to avoid memory issues."""
    
    # GTX 1060 optimized settings
    chunk_size = min(2000, total_samples // 3)  # Train in chunks
    max_steps_per_chunk = 500  # Short training sessions
    
    logging.info(f"🔧 GTX 1060 Optimized Training Configuration:")
    logging.info(f"   Config: {config}")
    logging.info(f"   Total samples: {total_samples:,}")
    logging.info(f"   Chunk size: {chunk_size:,}")
    logging.info(f"   Batch size: {batch_size}")
    logging.info(f"   Steps per chunk: {max_steps_per_chunk}")
    
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    
    # Calculate number of chunks
    num_chunks = (total_samples + chunk_size - 1) // chunk_size
    
    logging.info(f"📦 Training in {num_chunks} chunks to avoid memory overload")
    
    current_model_path = None
    
    for chunk_idx in range(num_chunks):
        start_sample = chunk_idx * chunk_size
        end_sample = min(start_sample + chunk_size, total_samples)
        actual_chunk_size = end_sample - start_sample
        
        logging.info(f"\n🚀 Training Chunk {chunk_idx + 1}/{num_chunks}")
        logging.info(f"   Samples: {start_sample:,} to {end_sample:,} ({actual_chunk_size:,} samples)")
        
        # Check GPU memory before each chunk
        if not check_gpu_memory():
            logging.warning("⚠️  Low GPU memory detected. Clearing cache...")
            torch.cuda.empty_cache()
            time.sleep(2)
        
        # Build training command for this chunk
        cmd = [
            sys.executable, "train.py",
            "--use-data-dir",
            "--config", config,
            "--max-samples", str(end_sample),  # Use cumulative samples
            "--batch-size", str(batch_size),
            "--max-steps", str(max_steps_per_chunk),
            "--learning-rate", "3e-5",  # Slightly lower for stability
            "--device", "cuda",
            "--output-dir", "models"
        ]
        
        # If we have a previous model, we could load it (for future enhancement)
        
        logging.info(f"🔄 Running: {' '.join(cmd)}")
        
        try:
            # Run training for this chunk
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            
            # Clear GPU memory after each chunk
            torch.cuda.empty_cache()
            
            logging.info(f"✅ Chunk {chunk_idx + 1} completed successfully!")
            
            # Find the latest model
            model_files = list(models_dir.glob("*.pt"))
            if model_files:
                current_model_path = max(model_files, key=lambda x: x.stat().st_mtime)
                logging.info(f"📁 Model saved: {current_model_path}")
            
            # Short break between chunks to let GPU cool down
            if chunk_idx < num_chunks - 1:
                logging.info("😴 Cooling down GPU for 5 seconds...")
                time.sleep(5)
                
        except subprocess.CalledProcessError as e:
            logging.error(f"❌ Chunk {chunk_idx + 1} failed: {e}")
            if e.stdout:
                logging.error(f"STDOUT: {e.stdout}")
            if e.stderr:
                logging.error(f"STDERR: {e.stderr}")
            
            # Try to continue with next chunk
            logging.info("🔄 Attempting to continue with next chunk...")
            continue
            
        except KeyboardInterrupt:
            logging.info("⏹️  Training interrupted by user")
            break
    
    return current_model_path


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="GTX 1060 Optimized TSNN Training")
    parser.add_argument(
        "--config",
        default="small",
        choices=["small", "medium", "energy_efficient"],
        help="Model configuration (optimized choices for GTX 1060)"
    )
    parser.add_argument(
        "--max-samples",
        type=int,
        default=8000,
        help="Maximum training samples (recommended: 5000-10000 for GTX 1060)"
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=1,
        help="Batch size (1-2 recommended for GTX 1060)"
    )
    parser.add_argument(
        "--quick-test",
        action="store_true",
        help="Quick test with minimal samples"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Quick test mode
    if args.quick_test:
        args.max_samples = 1000
        args.config = "small"
        logging.info("🧪 Quick test mode enabled")
    
    # Check CUDA availability
    if not torch.cuda.is_available():
        logging.error("❌ CUDA not available! Please check your installation.")
        return
    
    logging.info("🎮 CUDA detected! Optimizing for GTX 1060...")
    
    # Check if data directory exists
    data_dir = Path("data")
    if not data_dir.exists():
        logging.error("❌ data/ directory not found!")
        return
    
    # Check GPU memory
    if not check_gpu_memory():
        logging.error("❌ Insufficient GPU memory available!")
        return
    
    # GTX 1060 specific recommendations
    if args.config == "large":
        logging.warning("⚠️  'large' config may be too big for GTX 1060. Switching to 'medium'.")
        args.config = "medium"
    
    if args.batch_size > 2:
        logging.warning("⚠️  Batch size > 2 may cause memory issues. Reducing to 2.")
        args.batch_size = 2
    
    # Start incremental training
    logging.info("🚀 Starting GTX 1060 optimized training...")
    
    try:
        final_model = train_incrementally(
            config=args.config,
            total_samples=args.max_samples,
            batch_size=args.batch_size
        )
        
        if final_model:
            logging.info(f"\n🎉 Training completed successfully!")
            logging.info(f"📁 Final model: {final_model}")
            logging.info(f"\n💬 Test your trained AI:")
            logging.info(f"python chat.py --model-path {final_model} --config {args.config}")
            
            # Final GPU memory check
            torch.cuda.empty_cache()
            check_gpu_memory()
        else:
            logging.error("❌ Training failed - no model was saved")
            
    except Exception as e:
        logging.error(f"❌ Training failed with error: {e}")
        
    finally:
        # Clean up GPU memory
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            logging.info("🧹 GPU memory cleared")


if __name__ == "__main__":
    main()
