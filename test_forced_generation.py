#!/usr/bin/env python3
"""
Test Forced Generation - Try to get the model to generate actual content
"""

import torch
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tsnn.simple_tsnn import SimplifiedTSNNModel
from tsnn.tokenizer import TSNNTokenizer


def test_forced_generation():
    """Test generation with EOS blocking."""
    print("🔧 Testing Forced Generation (EOS Blocked)")
    print("=" * 50)
    
    # Load model
    model_path = "models/tsnn_full_final.pt"
    checkpoint = torch.load(model_path, map_location='cpu')
    config = checkpoint.get('config', {})
    
    tokenizer = TSNNTokenizer()
    model = SimplifiedTSNNModel(
        vocab_size=config.get('vocab_size', tokenizer.vocab_size),
        d_model=config.get('d_model', 512),
        n_heads=config.get('n_heads', 8),
        num_layers=config.get('num_layers', 8),
        d_ff=config.get('d_ff', 2048),
        max_seq_len=config.get('max_seq_len', 256),
        dropout=config.get('dropout', 0.1)
    )
    
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    print("✅ Model loaded with EOS blocking")
    
    # Test different inputs
    test_inputs = [
        "Hello",
        "What are you?",
        "How do you work?",
        "Tell me about TSNN"
    ]
    
    for user_input in test_inputs:
        print(f"\n🧪 Testing: '{user_input}'")
        
        # Create context
        context = f"User: {user_input}\nAI:"
        encoding = tokenizer.encode(context, max_length=50, padding=False, truncation=True)
        input_ids = encoding["input_ids"].unsqueeze(0)
        
        print(f"📝 Context: '{context}'")
        
        # Generate with EOS blocking
        try:
            with torch.no_grad():
                generated = model.generate(
                    input_ids=input_ids,
                    max_length=input_ids.shape[1] + 20,
                    temperature=1.2,  # Higher temperature
                    do_sample=True,
                    top_k=30,
                    top_p=0.8,
                    pad_token_id=tokenizer.pad_token_id
                )
            
            # Decode
            generated_part = generated[:, input_ids.shape[1]:]
            response = tokenizer.decode(generated_part[0], skip_special_tokens=True)
            
            print(f"🤖 Raw response: '{response}'")
            
            # Clean
            if "User:" in response:
                response = response.split("User:")[0].strip()
            response = " ".join(response.split())
            
            print(f"✨ Clean response: '{response}'")
            
        except Exception as e:
            print(f"❌ Error: {e}")


def test_alternative_prompts():
    """Test with different prompt formats."""
    print("\n🎯 Testing Alternative Prompt Formats")
    print("=" * 40)
    
    # Load model
    model_path = "models/tsnn_full_final.pt"
    checkpoint = torch.load(model_path, map_location='cpu')
    config = checkpoint.get('config', {})
    
    tokenizer = TSNNTokenizer()
    model = SimplifiedTSNNModel(
        vocab_size=config.get('vocab_size', tokenizer.vocab_size),
        d_model=config.get('d_model', 512),
        n_heads=config.get('n_heads', 8),
        num_layers=config.get('num_layers', 8),
        d_ff=config.get('d_ff', 2048),
        max_seq_len=config.get('max_seq_len', 256),
        dropout=config.get('dropout', 0.1)
    )
    
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    # Try different prompt formats
    prompt_formats = [
        "Hello, I am",  # Direct completion
        "AI: Hello! I am",  # Start with AI response
        "The AI said: I am",  # Third person
        "Question: What are you?\nAnswer:",  # Q&A format
    ]
    
    for prompt in prompt_formats:
        print(f"\n🧪 Prompt: '{prompt}'")
        
        encoding = tokenizer.encode(prompt, max_length=30, padding=False, truncation=True)
        input_ids = encoding["input_ids"].unsqueeze(0)
        
        try:
            with torch.no_grad():
                generated = model.generate(
                    input_ids=input_ids,
                    max_length=input_ids.shape[1] + 15,
                    temperature=1.0,
                    do_sample=True,
                    top_k=20,
                    top_p=0.9
                )
            
            generated_part = generated[:, input_ids.shape[1]:]
            response = tokenizer.decode(generated_part[0], skip_special_tokens=True)
            
            print(f"🤖 Response: '{response}'")
            
        except Exception as e:
            print(f"❌ Error: {e}")


if __name__ == "__main__":
    test_forced_generation()
    test_alternative_prompts()
