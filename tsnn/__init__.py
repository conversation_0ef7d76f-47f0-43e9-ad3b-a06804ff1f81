"""
Transformer Spiking Neural Network (TSNN) Architecture
Proprietary model by Triton Software Labs

A neuromorphic AI model combining biological plausibility of spiking neural networks
with the sequence modeling power of transformers, using discrete spike-based communication
for energy-efficient, event-driven processing.
"""

from .spiking_neuron import SpikingNeuron, LeakyIntegrateFireNeuron
from .spiking_attention import Spiking<PERSON><PERSON><PERSON>
from .tsnn_layer import TSNNLayer
from .tsnn_model import TSNNModel
from .tokenizer import TSNNTokenizer

__version__ = "1.0.0"
__author__ = "Triton Software Labs"
__description__ = "Transformer Spiking Neural Network for Neuromorphic AI"
