"""
Spiking Attention Mechanism for TSNN
Proprietary Triton Software Labs Architecture

Implements self-attention using spike-based computation with temporal dynamics,
replacing traditional matrix multiplications with event-driven processing.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Optional, Tuple
from .spiking_neuron import SpikingLinear, LeakyIntegrateFireNeuron


class SpikingAttention(nn.Module):
    """
    Spike-based self-attention mechanism for TSNN.
    
    Key innovations:
    - Spike-timing dependent attention weights
    - Event-driven query-key-value computation
    - Temporal correlation-based attention scores
    - Energy-efficient sparse processing
    """
    
    def __init__(self,
                 d_model: int,
                 n_heads: int = 8,
                 dropout: float = 0.1,
                 spike_threshold: float = 1.0,
                 temporal_window: int = 10):
        super().__init__()
        assert d_model % n_heads == 0
        
        self.d_model = d_model
        self.n_heads = n_heads
        self.d_k = d_model // n_heads
        self.temporal_window = temporal_window
        
        # Spiking linear layers for Q, K, V projections
        self.w_q = SpikingLinear(d_model, d_model, neuron_type="lif")
        self.w_k = SpikingLinear(d_model, d_model, neuron_type="lif")
        self.w_v = SpikingLinear(d_model, d_model, neuron_type="lif")
        self.w_o = SpikingLinear(d_model, d_model, neuron_type="lif")
        
        # Spike-based attention computation
        self.attention_neurons = nn.ModuleList([
            LeakyIntegrateFireNeuron(threshold=spike_threshold)
            for _ in range(n_heads)
        ])
        
        self.dropout = nn.Dropout(dropout)
        self.scale = 1.0 / math.sqrt(self.d_k)
        
        # Temporal correlation tracking
        self.register_buffer('spike_history', torch.zeros(1, 1, d_model))
        
    def reset_state(self, batch_size: int, device: torch.device):
        """Reset attention state for new sequence."""
        self.w_q.reset_state(batch_size, device)
        self.w_k.reset_state(batch_size, device)
        self.w_v.reset_state(batch_size, device)
        self.w_o.reset_state(batch_size, device)
        
        for neuron in self.attention_neurons:
            neuron.reset_state(batch_size * self.n_heads, device)
            
        # Reset spike history
        self.spike_history = torch.zeros(
            batch_size, self.temporal_window, self.d_model, device=device
        )
    
    def spike_based_attention_scores(self, 
                                   q_spikes: torch.Tensor, 
                                   k_spikes: torch.Tensor) -> torch.Tensor:
        """
        Compute attention scores using spike timing and correlation.
        
        Args:
            q_spikes: Query spikes [batch_size, seq_len, d_model]
            k_spikes: Key spikes [batch_size, seq_len, d_model]
            
        Returns:
            attention_scores: Spike-based attention weights
        """
        batch_size, seq_len, d_model = q_spikes.shape
        
        # Reshape for multi-head attention
        q_spikes = q_spikes.view(batch_size, seq_len, self.n_heads, self.d_k)
        k_spikes = k_spikes.view(batch_size, seq_len, self.n_heads, self.d_k)
        
        # Compute spike correlation-based attention
        attention_scores = torch.zeros(
            batch_size, self.n_heads, seq_len, seq_len, 
            device=q_spikes.device
        )
        
        for i in range(seq_len):
            for j in range(seq_len):
                # Spike correlation between query and key
                q_i = q_spikes[:, i, :, :]  # [batch_size, n_heads, d_k]
                k_j = k_spikes[:, j, :, :]  # [batch_size, n_heads, d_k]
                
                # Compute spike coincidence (AND operation)
                spike_coincidence = q_i * k_j  # Element-wise multiplication
                
                # Sum over feature dimension and apply scaling
                correlation = torch.sum(spike_coincidence, dim=-1) * self.scale
                
                # Apply temporal decay based on distance
                temporal_decay = torch.exp(torch.tensor(-0.1 * abs(i - j), device=correlation.device))
                correlation = correlation * temporal_decay
                
                attention_scores[:, :, i, j] = correlation
        
        return attention_scores
    
    def spike_timing_dependent_plasticity(self, 
                                        pre_spikes: torch.Tensor,
                                        post_spikes: torch.Tensor,
                                        tau: float = 20.0) -> torch.Tensor:
        """
        Apply spike-timing dependent plasticity for attention modulation.
        
        Args:
            pre_spikes: Pre-synaptic spikes
            post_spikes: Post-synaptic spikes
            tau: Time constant for STDP
            
        Returns:
            plasticity_weights: STDP-based weight modulation
        """
        batch_size, seq_len, _ = pre_spikes.shape
        
        # Compute spike timing differences
        plasticity_weights = torch.ones_like(pre_spikes[:, :, 0:1])
        
        for t in range(1, seq_len):
            # Look for spike pairs within temporal window
            for dt in range(1, min(self.temporal_window, t + 1)):
                if t - dt >= 0:
                    # Pre-before-post: potentiation
                    pre_before = pre_spikes[:, t - dt, :]
                    post_after = post_spikes[:, t, :]
                    potentiation = torch.sum(pre_before * post_after, dim=-1, keepdim=True)
                    exp_factor = torch.exp(torch.tensor(-dt / tau, device=potentiation.device))
                    plasticity_weights[:, t, :] += 0.1 * potentiation * exp_factor

                    # Post-before-pre: depression
                    post_before = post_spikes[:, t - dt, :]
                    pre_after = pre_spikes[:, t, :]
                    depression = torch.sum(post_before * pre_after, dim=-1, keepdim=True)
                    exp_factor = torch.exp(torch.tensor(-dt / tau, device=depression.device))
                    plasticity_weights[:, t, :] -= 0.05 * depression * exp_factor
        
        return torch.clamp(plasticity_weights, min=0.1, max=2.0)
    
    def forward(self, 
                x: torch.Tensor, 
                mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Forward pass of spiking attention.
        
        Args:
            x: Input spikes [batch_size, seq_len, d_model]
            mask: Attention mask [batch_size, seq_len, seq_len]
            
        Returns:
            output: Attended output spikes [batch_size, seq_len, d_model]
        """
        batch_size, seq_len, d_model = x.shape
        
        # Reset states if needed
        if self.w_q.neurons[0].membrane_potential is None:
            self.reset_state(batch_size, x.device)
        
        # Generate Q, K, V through spiking linear layers
        q_spikes = self.w_q(x)  # [batch_size, seq_len, d_model]
        k_spikes = self.w_k(x)  # [batch_size, seq_len, d_model]
        v_spikes = self.w_v(x)  # [batch_size, seq_len, d_model]
        
        # Compute spike-based attention scores
        attention_scores = self.spike_based_attention_scores(q_spikes, k_spikes)
        
        # Apply mask if provided
        if mask is not None:
            # Handle different mask dimensions
            if mask.dim() == 2:  # [batch_size, seq_len]
                mask = mask.unsqueeze(1).unsqueeze(1)  # [batch_size, 1, 1, seq_len]
                mask = mask.expand(-1, self.n_heads, seq_len, -1)  # [batch_size, n_heads, seq_len, seq_len]
            elif mask.dim() == 3:  # [batch_size, seq_len, seq_len]
                mask = mask.unsqueeze(1).expand(-1, self.n_heads, -1, -1)
            elif mask.dim() == 4:  # Already correct shape
                pass
            else:
                # Squeeze extra dimensions
                while mask.dim() > 4:
                    mask = mask.squeeze(1)
                if mask.dim() == 3:
                    mask = mask.unsqueeze(1).expand(-1, self.n_heads, -1, -1)

            attention_scores = attention_scores.masked_fill(mask == 0, -1e9)
        
        # Convert attention scores to probabilities
        attention_weights = F.softmax(attention_scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        # Apply attention to values
        v_spikes = v_spikes.view(batch_size, seq_len, self.n_heads, self.d_k)
        v_spikes = v_spikes.transpose(1, 2)  # [batch_size, n_heads, seq_len, d_k]
        
        # Weighted combination of value spikes
        attended_spikes = torch.matmul(attention_weights, v_spikes)
        
        # Reshape back to original dimensions
        attended_spikes = attended_spikes.transpose(1, 2).contiguous().view(
            batch_size, seq_len, d_model
        )
        
        # Apply STDP-based plasticity modulation
        plasticity_weights = self.spike_timing_dependent_plasticity(
            x, attended_spikes
        )
        attended_spikes = attended_spikes * plasticity_weights
        
        # Final output projection
        output = self.w_o(attended_spikes)
        
        # Update spike history for temporal correlation tracking
        self.spike_history = torch.cat([
            self.spike_history[:, 1:, :],
            output[:, -1:, :]
        ], dim=1)
        
        return output


class SpikingMultiHeadAttention(nn.Module):
    """
    Multi-head spiking attention with enhanced temporal processing.
    """
    
    def __init__(self,
                 d_model: int,
                 n_heads: int = 8,
                 dropout: float = 0.1,
                 temporal_window: int = 10):
        super().__init__()
        
        self.attention = SpikingAttention(
            d_model=d_model,
            n_heads=n_heads,
            dropout=dropout,
            temporal_window=temporal_window
        )
        
        # Layer normalization adapted for spikes
        self.layer_norm = nn.LayerNorm(d_model)
        
    def forward(self, 
                x: torch.Tensor, 
                mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Forward pass with residual connection and normalization.
        
        Args:
            x: Input spikes [batch_size, seq_len, d_model]
            mask: Attention mask
            
        Returns:
            output: Processed spikes with residual connection
        """
        # Apply spiking attention
        attended = self.attention(x, mask)
        
        # Residual connection (adapted for spikes)
        # Use weighted combination instead of direct addition
        residual_weight = 0.7
        output = residual_weight * x + (1 - residual_weight) * attended
        
        # Apply layer normalization to continuous values
        # Convert spikes to continuous for normalization, then back to spikes
        continuous_output = torch.tanh(output)  # Smooth activation
        normalized = self.layer_norm(continuous_output)
        
        # Convert back to spikes using threshold
        spike_output = (normalized > 0.5).float()
        
        return spike_output
