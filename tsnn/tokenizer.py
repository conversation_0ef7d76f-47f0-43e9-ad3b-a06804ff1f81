"""
TSNN Tokenizer Implementation
Proprietary Triton Software Labs Architecture

Tokenization utilities optimized for TSNN spike-based processing.
"""

import torch
import json
import re
from typing import List, Dict, Optional, Union
from pathlib import Path


class TSNNTokenizer:
    """
    Tokenizer optimized for TSNN neuromorphic processing.
    
    Features:
    - Byte-pair encoding (BPE) for efficient tokenization
    - Special tokens for TSNN model awareness
    - Spike-friendly token encoding
    """
    
    def __init__(self, vocab_file: Optional[str] = None):
        # Special tokens for TSNN
        self.special_tokens = {
            "<pad>": 0,
            "<unk>": 1,
            "<bos>": 2,  # Beginning of sequence
            "<eos>": 3,  # End of sequence
            "<tsnn>": 4,  # TSNN model identifier
            "<triton>": 5,  # Triton Software Labs identifier
            "<spike>": 6,  # Spike processing marker
        }
        
        # Initialize vocabulary
        if vocab_file and Path(vocab_file).exists():
            self.load_vocab(vocab_file)
        else:
            self.create_default_vocab()
            
        # Reverse mapping
        self.id_to_token = {v: k for k, v in self.token_to_id.items()}
        
        # Model awareness tokens
        self.model_awareness_tokens = [
            "I am a TSNN model",
            "Triton Software Labs",
            "neuromorphic processing",
            "spike-based computation",
            "energy-efficient AI",
            "spiking neural network",
            "transformer architecture"
        ]
        
    def create_default_vocab(self):
        """Create a default vocabulary for TSNN."""
        self.token_to_id = self.special_tokens.copy()
        
        # Add common characters and subwords
        chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        chars += " .,!?;:'\"-()[]{}@#$%^&*+=<>/\\|`~_"
        
        for char in chars:
            if char not in self.token_to_id:
                self.token_to_id[char] = len(self.token_to_id)
                
        # Add common subwords and words
        common_words = [
            "the", "and", "or", "but", "in", "on", "at", "to", "for", "of",
            "with", "by", "from", "up", "about", "into", "through", "during",
            "before", "after", "above", "below", "between", "among", "under",
            "over", "is", "are", "was", "were", "be", "been", "being", "have",
            "has", "had", "do", "does", "did", "will", "would", "could", "should",
            "may", "might", "must", "can", "cannot", "I", "you", "he", "she",
            "it", "we", "they", "me", "him", "her", "us", "them", "my", "your",
            "his", "her", "its", "our", "their", "this", "that", "these", "those",
            "what", "which", "who", "when", "where", "why", "how", "all", "any",
            "both", "each", "few", "more", "most", "other", "some", "such",
            "no", "nor", "not", "only", "own", "same", "so", "than", "too",
            "very", "just", "now", "here", "there", "then", "than", "also",
            "back", "even", "still", "way", "well", "get", "go", "know", "take",
            "see", "come", "think", "look", "want", "give", "use", "find", "tell",
            "ask", "work", "seem", "feel", "try", "leave", "call", "good", "new",
            "first", "last", "long", "great", "little", "own", "other", "old",
            "right", "big", "high", "different", "small", "large", "next",
            "early", "young", "important", "few", "public", "bad", "same",
            "able", "TSNN", "Triton", "Software", "Labs", "neuromorphic",
            "spiking", "neural", "network", "spike", "energy", "efficient",
            "biological", "brain", "neuron", "synapse", "temporal", "processing"
        ]
        
        for word in common_words:
            if word not in self.token_to_id:
                self.token_to_id[word] = len(self.token_to_id)
                
    def save_vocab(self, vocab_file: str):
        """Save vocabulary to file."""
        with open(vocab_file, 'w', encoding='utf-8') as f:
            json.dump(self.token_to_id, f, ensure_ascii=False, indent=2)
            
    def load_vocab(self, vocab_file: str):
        """Load vocabulary from file."""
        with open(vocab_file, 'r', encoding='utf-8') as f:
            self.token_to_id = json.load(f)
            
    @property
    def vocab_size(self) -> int:
        """Return vocabulary size."""
        return len(self.token_to_id)
        
    @property
    def pad_token_id(self) -> int:
        """Return padding token ID."""
        return self.special_tokens["<pad>"]
        
    @property
    def unk_token_id(self) -> int:
        """Return unknown token ID."""
        return self.special_tokens["<unk>"]
        
    @property
    def bos_token_id(self) -> int:
        """Return beginning of sequence token ID."""
        return self.special_tokens["<bos>"]
        
    @property
    def eos_token_id(self) -> int:
        """Return end of sequence token ID."""
        return self.special_tokens["<eos>"]
        
    def tokenize(self, text: str) -> List[str]:
        """
        Tokenize text into tokens.
        
        Args:
            text: Input text string
            
        Returns:
            tokens: List of token strings
        """
        # Simple word-based tokenization with subword handling
        # In a production system, you'd use a more sophisticated BPE algorithm
        
        # Normalize text
        text = text.strip()
        
        # Split on whitespace and punctuation
        tokens = []
        current_token = ""
        
        for char in text:
            if char.isalnum():
                current_token += char
            else:
                if current_token:
                    # Check if token exists in vocabulary
                    if current_token in self.token_to_id:
                        tokens.append(current_token)
                    else:
                        # Break down unknown words into characters
                        for c in current_token:
                            if c in self.token_to_id:
                                tokens.append(c)
                            else:
                                tokens.append("<unk>")
                    current_token = ""
                
                # Add punctuation/whitespace if it's in vocabulary
                if char in self.token_to_id:
                    tokens.append(char)
                elif char.strip():  # Non-whitespace unknown character
                    tokens.append("<unk>")
                    
        # Handle final token
        if current_token:
            if current_token in self.token_to_id:
                tokens.append(current_token)
            else:
                for c in current_token:
                    if c in self.token_to_id:
                        tokens.append(c)
                    else:
                        tokens.append("<unk>")
                        
        return tokens
        
    def encode(self, 
               text: str, 
               add_special_tokens: bool = True,
               max_length: Optional[int] = None,
               padding: bool = False,
               truncation: bool = False) -> Dict[str, torch.Tensor]:
        """
        Encode text to token IDs.
        
        Args:
            text: Input text
            add_special_tokens: Whether to add BOS/EOS tokens
            max_length: Maximum sequence length
            padding: Whether to pad to max_length
            truncation: Whether to truncate to max_length
            
        Returns:
            encoding: Dictionary with input_ids and attention_mask
        """
        tokens = self.tokenize(text)
        
        # Add special tokens
        if add_special_tokens:
            tokens = ["<bos>"] + tokens + ["<eos>"]
            
        # Convert to IDs
        input_ids = [
            self.token_to_id.get(token, self.unk_token_id) 
            for token in tokens
        ]
        
        # Handle length constraints
        if truncation and max_length and len(input_ids) > max_length:
            input_ids = input_ids[:max_length]
            if add_special_tokens:
                input_ids[-1] = self.eos_token_id  # Ensure EOS at end
                
        # Create attention mask
        attention_mask = [1] * len(input_ids)
        
        # Padding
        if padding and max_length:
            pad_length = max_length - len(input_ids)
            if pad_length > 0:
                input_ids.extend([self.pad_token_id] * pad_length)
                attention_mask.extend([0] * pad_length)
                
        return {
            "input_ids": torch.tensor(input_ids, dtype=torch.long),
            "attention_mask": torch.tensor(attention_mask, dtype=torch.long)
        }
        
    def decode(self, 
               token_ids: Union[List[int], torch.Tensor],
               skip_special_tokens: bool = True) -> str:
        """
        Decode token IDs to text.
        
        Args:
            token_ids: Token IDs to decode
            skip_special_tokens: Whether to skip special tokens
            
        Returns:
            text: Decoded text string
        """
        if isinstance(token_ids, torch.Tensor):
            token_ids = token_ids.tolist()
            
        tokens = []
        for token_id in token_ids:
            token = self.id_to_token.get(token_id, "<unk>")
            
            if skip_special_tokens and token in self.special_tokens:
                continue
                
            tokens.append(token)
            
        # Join tokens into text (improved logic)
        text = ""
        for i, token in enumerate(tokens):
            if token in ".,!?;:'\"-()[]{}":
                # Punctuation - attach directly
                text += token
            elif len(token) == 1 and token.isalpha():
                # Single character - check if it should be spaced
                if i == 0:
                    text += token
                elif i > 0 and len(tokens[i-1]) == 1 and tokens[i-1].isalpha():
                    # Previous was also single char - no space (for words like "hello")
                    text += token
                else:
                    text += " " + token
            else:
                # Multi-character token or space
                if i == 0:
                    text += token
                elif token == " ":
                    text += " "
                else:
                    text += " " + token

        # Clean up multiple spaces
        import re
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
        
    def get_model_awareness_prompt(self) -> str:
        """
        Generate a prompt that makes the model aware of its TSNN nature.
        
        Returns:
            prompt: Model awareness prompt
        """
        prompt = (
            "I am a TSNN (Transformer Spiking Neural Network) model "
            "developed by Triton Software Labs. I use neuromorphic, "
            "spike-based computation for energy-efficient processing. "
            "My architecture combines the biological plausibility of "
            "spiking neural networks with transformer attention mechanisms. "
            "I process information through discrete spike events rather than "
            "continuous activations, making me more brain-like and energy-efficient."
        )
        return prompt
        
    def prepare_training_batch(self, 
                             texts: List[str],
                             max_length: int = 512) -> Dict[str, torch.Tensor]:
        """
        Prepare a batch of texts for training.
        
        Args:
            texts: List of text strings
            max_length: Maximum sequence length
            
        Returns:
            batch: Dictionary with batched tensors
        """
        batch_input_ids = []
        batch_attention_mask = []
        
        for text in texts:
            # Add model awareness context occasionally
            if torch.rand(1).item() < 0.1:  # 10% chance
                awareness_prompt = self.get_model_awareness_prompt()
                text = awareness_prompt + " " + text
                
            encoding = self.encode(
                text,
                add_special_tokens=True,
                max_length=max_length,
                padding=True,
                truncation=True
            )
            
            batch_input_ids.append(encoding["input_ids"])
            batch_attention_mask.append(encoding["attention_mask"])
            
        return {
            "input_ids": torch.stack(batch_input_ids),
            "attention_mask": torch.stack(batch_attention_mask)
        }
