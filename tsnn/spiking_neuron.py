"""
Spiking Neuron Implementation for TSNN
Proprietary Triton Software Labs Architecture

Implements various spiking neuron models for neuromorphic computation
with discrete spike-based communication and temporal dynamics.
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Tuple, Optional


class SpikingNeuron(nn.Module):
    """Base class for spiking neurons with temporal dynamics."""
    
    def __init__(self, threshold: float = 1.0, reset_potential: float = 0.0):
        super().__init__()
        self.threshold = threshold
        self.reset_potential = reset_potential
        self.membrane_potential = None
        
    def reset_state(self, batch_size: int, device: torch.device):
        """Reset neuron state for new sequence."""
        self.membrane_potential = torch.zeros(batch_size, device=device)
        
    def forward(self, input_current: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Forward pass returning spikes and membrane potential."""
        raise NotImplementedError


class LeakyIntegrateFireNeuron(SpikingNeuron):
    """
    Leaky Integrate-and-Fire neuron model for TSNN.
    
    Implements biological-like neuron dynamics with:
    - Membrane potential integration
    - Leak current for temporal decay
    - Threshold-based spike generation
    - Reset after spike
    """
    
    def __init__(self, 
                 threshold: float = 1.0,
                 reset_potential: float = 0.0,
                 leak_factor: float = 0.9,
                 refractory_period: int = 1):
        super().__init__(threshold, reset_potential)
        self.leak_factor = leak_factor
        self.refractory_period = refractory_period
        self.refractory_counter = None
        
    def reset_state(self, batch_size: int, device: torch.device):
        """Reset neuron state for new sequence."""
        super().reset_state(batch_size, device)
        self.refractory_counter = torch.zeros(batch_size, dtype=torch.int, device=device)
        
    def forward(self, input_current: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass of LIF neuron.
        
        Args:
            input_current: Input current tensor [batch_size, ...]
            
        Returns:
            spikes: Binary spike tensor [batch_size, ...]
            membrane_potential: Current membrane potential [batch_size, ...]
        """
        if self.membrane_potential is None:
            self.reset_state(input_current.shape[0], input_current.device)
            
        # Apply leak to membrane potential
        self.membrane_potential = self.membrane_potential * self.leak_factor
        
        # Add input current (only if not in refractory period)
        not_refractory = (self.refractory_counter == 0).float()
        self.membrane_potential += input_current.squeeze() * not_refractory
        
        # Generate spikes where membrane potential exceeds threshold
        spikes = (self.membrane_potential >= self.threshold).float()
        
        # Reset membrane potential where spikes occurred
        self.membrane_potential = torch.where(
            spikes.bool(),
            torch.full_like(self.membrane_potential, self.reset_potential),
            self.membrane_potential
        )
        
        # Update refractory counter
        self.refractory_counter = torch.where(
            spikes.bool(),
            torch.full_like(self.refractory_counter, self.refractory_period),
            torch.clamp(self.refractory_counter - 1, min=0)
        )
        
        return spikes.unsqueeze(-1), self.membrane_potential.unsqueeze(-1)


class AdaptiveSpikingNeuron(SpikingNeuron):
    """
    Adaptive spiking neuron with dynamic threshold.
    
    Features:
    - Adaptive threshold based on recent activity
    - Spike frequency adaptation
    - Enhanced temporal dynamics for sequence processing
    """
    
    def __init__(self,
                 base_threshold: float = 1.0,
                 reset_potential: float = 0.0,
                 leak_factor: float = 0.9,
                 adaptation_factor: float = 0.1,
                 threshold_decay: float = 0.95):
        super().__init__(base_threshold, reset_potential)
        self.base_threshold = base_threshold
        self.leak_factor = leak_factor
        self.adaptation_factor = adaptation_factor
        self.threshold_decay = threshold_decay
        self.adaptive_threshold = None
        
    def reset_state(self, batch_size: int, device: torch.device):
        """Reset neuron state for new sequence."""
        super().reset_state(batch_size, device)
        self.adaptive_threshold = torch.full(
            (batch_size,), self.base_threshold, device=device
        )
        
    def forward(self, input_current: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Forward pass with adaptive threshold."""
        if self.membrane_potential is None:
            self.reset_state(input_current.shape[0], input_current.device)
            
        # Apply leak
        self.membrane_potential = self.membrane_potential * self.leak_factor
        
        # Add input current
        self.membrane_potential += input_current.squeeze()
        
        # Generate spikes using adaptive threshold
        spikes = (self.membrane_potential >= self.adaptive_threshold).float()
        
        # Reset membrane potential where spikes occurred
        self.membrane_potential = torch.where(
            spikes.bool(),
            torch.full_like(self.membrane_potential, self.reset_potential),
            self.membrane_potential
        )
        
        # Update adaptive threshold
        self.adaptive_threshold = torch.where(
            spikes.bool(),
            self.adaptive_threshold + self.adaptation_factor,
            self.adaptive_threshold * self.threshold_decay
        )
        
        # Ensure threshold doesn't go below base threshold
        self.adaptive_threshold = torch.clamp(
            self.adaptive_threshold, min=self.base_threshold
        )
        
        return spikes.unsqueeze(-1), self.membrane_potential.unsqueeze(-1)


class SpikingLinear(nn.Module):
    """
    Linear layer with spiking neurons for TSNN.
    
    Replaces traditional matrix multiplication with spike-based computation
    for energy-efficient processing.
    """
    
    def __init__(self, 
                 in_features: int,
                 out_features: int,
                 neuron_type: str = "lif",
                 bias: bool = True):
        super().__init__()
        self.in_features = in_features
        self.out_features = out_features
        
        # Weight matrix (still needed for spike-based computation)
        self.weight = nn.Parameter(torch.randn(out_features, in_features))
        if bias:
            self.bias = nn.Parameter(torch.zeros(out_features))
        else:
            self.register_parameter('bias', None)
            
        # Spiking neurons
        if neuron_type == "lif":
            self.neurons = nn.ModuleList([
                LeakyIntegrateFireNeuron() for _ in range(out_features)
            ])
        elif neuron_type == "adaptive":
            self.neurons = nn.ModuleList([
                AdaptiveSpikingNeuron() for _ in range(out_features)
            ])
        else:
            raise ValueError(f"Unknown neuron type: {neuron_type}")
            
    def reset_state(self, batch_size: int, device: torch.device):
        """Reset all neuron states."""
        for neuron in self.neurons:
            neuron.reset_state(batch_size, device)
            
    def forward(self, spike_input: torch.Tensor) -> torch.Tensor:
        """
        Forward pass with spike-based computation.
        
        Args:
            spike_input: Input spikes [batch_size, seq_len, in_features]
            
        Returns:
            output_spikes: Output spikes [batch_size, seq_len, out_features]
        """
        batch_size, seq_len, _ = spike_input.shape
        
        if self.neurons[0].membrane_potential is None:
            self.reset_state(batch_size, spike_input.device)
            
        output_spikes = []
        
        for t in range(seq_len):
            # Current input spikes at time t
            current_spikes = spike_input[:, t, :]  # [batch_size, in_features]
            
            # Compute weighted input current for each output neuron
            neuron_outputs = []
            for i, neuron in enumerate(self.neurons):
                # Weighted sum of input spikes
                input_current = torch.sum(
                    current_spikes * self.weight[i], dim=1, keepdim=True
                )
                if self.bias is not None:
                    input_current += self.bias[i]
                    
                # Generate output spike
                spike, _ = neuron(input_current)
                neuron_outputs.append(spike)
                
            # Stack outputs from all neurons
            timestep_output = torch.cat(neuron_outputs, dim=1)  # [batch_size, out_features]
            output_spikes.append(timestep_output)
            
        return torch.stack(output_spikes, dim=1)  # [batch_size, seq_len, out_features]
