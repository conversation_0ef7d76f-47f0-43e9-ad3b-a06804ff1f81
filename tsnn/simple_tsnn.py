"""
Simplified TSNN Model for Practical Training
Proprietary Triton Software Labs

A more computationally efficient version of TSNN that maintains
the core neuromorphic concepts while being trainable.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Dict, Any
import math


class SimplifiedSpikingLayer(nn.Module):
    """Simplified spiking layer that approximates spike behavior."""
    
    def __init__(self, d_model: int, threshold: float = 0.5):
        super().__init__()
        self.d_model = d_model
        self.threshold = threshold
        
        # Learnable parameters for spike dynamics
        self.spike_transform = nn.Linear(d_model, d_model)
        self.membrane_decay = nn.Parameter(torch.tensor(0.9))
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Simplified spike generation using differentiable approximation.
        
        Args:
            x: Input tensor [batch_size, seq_len, d_model]
            
        Returns:
            spikes: Spike-like activations [batch_size, seq_len, d_model]
        """
        # Transform input
        transformed = self.spike_transform(x)
        
        # Apply membrane dynamics (simplified)
        membrane_potential = transformed * self.membrane_decay
        
        # Generate spikes using sigmoid approximation (differentiable)
        # This approximates the step function while remaining trainable
        spikes = torch.sigmoid((membrane_potential - self.threshold) * 10.0)
        
        # Add sparsity regularization during training
        if self.training:
            # Encourage sparsity (most values should be close to 0 or 1)
            sparsity_loss = torch.mean(spikes * (1 - spikes))  # Minimizes values around 0.5
            # Store for potential use in loss function
            if not hasattr(self, '_sparsity_loss'):
                self._sparsity_loss = sparsity_loss
        
        return spikes


class SimplifiedSpikingAttention(nn.Module):
    """Simplified spike-based attention mechanism."""
    
    def __init__(self, d_model: int, n_heads: int = 8, dropout: float = 0.1):
        super().__init__()
        assert d_model % n_heads == 0
        
        self.d_model = d_model
        self.n_heads = n_heads
        self.d_k = d_model // n_heads
        self.scale = 1.0 / math.sqrt(self.d_k)
        
        # Standard linear projections
        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)
        
        # Spiking layers
        self.spike_q = SimplifiedSpikingLayer(d_model)
        self.spike_k = SimplifiedSpikingLayer(d_model)
        self.spike_v = SimplifiedSpikingLayer(d_model)
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Simplified spiking attention forward pass.
        
        Args:
            x: Input tensor [batch_size, seq_len, d_model]
            mask: Attention mask [batch_size, seq_len, seq_len]
            
        Returns:
            output: Attended output [batch_size, seq_len, d_model]
        """
        batch_size, seq_len, d_model = x.shape
        
        # Generate Q, K, V
        q = self.w_q(x)
        k = self.w_k(x)
        v = self.w_v(x)
        
        # Apply spiking transformations
        q_spikes = self.spike_q(q)
        k_spikes = self.spike_k(k)
        v_spikes = self.spike_v(v)
        
        # Reshape for multi-head attention
        q_spikes = q_spikes.view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        k_spikes = k_spikes.view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        v_spikes = v_spikes.view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        
        # Compute attention scores (using spike correlations)
        attention_scores = torch.matmul(q_spikes, k_spikes.transpose(-2, -1)) * self.scale
        
        # Apply mask if provided
        if mask is not None:
            # Handle different mask dimensions properly
            if mask.dim() == 2:  # [seq_len, seq_len]
                mask = mask.unsqueeze(0).unsqueeze(0)  # [1, 1, seq_len, seq_len]
                mask = mask.expand(batch_size, self.n_heads, -1, -1)  # [batch_size, n_heads, seq_len, seq_len]
            elif mask.dim() == 3:  # [batch_size, seq_len, seq_len]
                mask = mask.unsqueeze(1)  # [batch_size, 1, seq_len, seq_len]
                mask = mask.expand(-1, self.n_heads, -1, -1)  # [batch_size, n_heads, seq_len, seq_len]
            elif mask.dim() == 4:  # Already correct shape [batch_size, n_heads, seq_len, seq_len]
                pass
            else:
                # Fallback: reshape to correct dimensions
                mask = mask.view(batch_size, 1, seq_len, seq_len)
                mask = mask.expand(-1, self.n_heads, -1, -1)

            attention_scores = attention_scores.masked_fill(mask == 0, -1e9)
        
        # Apply softmax
        attention_weights = F.softmax(attention_scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        # Apply attention to values
        attended = torch.matmul(attention_weights, v_spikes)
        
        # Reshape back
        attended = attended.transpose(1, 2).contiguous().view(
            batch_size, seq_len, d_model
        )
        
        # Final projection
        output = self.w_o(attended)
        
        return output


class SimplifiedTSNNLayer(nn.Module):
    """Simplified TSNN transformer layer."""
    
    def __init__(self, d_model: int, n_heads: int = 8, d_ff: int = None, dropout: float = 0.1):
        super().__init__()
        
        if d_ff is None:
            d_ff = 4 * d_model
            
        self.attention = SimplifiedSpikingAttention(d_model, n_heads, dropout)
        
        # Feed-forward network with spiking
        self.ff1 = nn.Linear(d_model, d_ff)
        self.spike_ff = SimplifiedSpikingLayer(d_ff)
        self.ff2 = nn.Linear(d_ff, d_model)
        
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Forward pass of simplified TSNN layer."""
        
        # Self-attention with residual connection
        attn_output = self.attention(x, mask)
        x = self.norm1(x + self.dropout(attn_output))
        
        # Feed-forward with spiking and residual connection
        ff_output = self.ff1(x)
        ff_spikes = self.spike_ff(ff_output)
        ff_output = self.ff2(ff_spikes)
        x = self.norm2(x + self.dropout(ff_output))
        
        return x


class SimplifiedTSNNModel(nn.Module):
    """
    Simplified TSNN model that maintains neuromorphic concepts
    while being computationally efficient and trainable.
    """
    
    def __init__(self,
                 vocab_size: int,
                 d_model: int = 512,
                 n_heads: int = 8,
                 num_layers: int = 6,
                 d_ff: int = None,
                 max_seq_len: int = 512,
                 dropout: float = 0.1):
        super().__init__()
        
        self.vocab_size = vocab_size
        self.d_model = d_model
        self.max_seq_len = max_seq_len
        
        # Model identification
        self.model_info = {
            "name": "Simplified TSNN",
            "architecture": "Transformer Spiking Neural Network (Efficient)",
            "company": "Triton Software Labs",
            "version": "1.0.0",
            "neuromorphic": True,
            "spike_based": True
        }
        
        # Embeddings
        self.token_embedding = nn.Embedding(vocab_size, d_model)
        self.pos_embedding = nn.Embedding(max_seq_len, d_model)
        
        # Spiking embedding transformation
        self.embedding_spikes = SimplifiedSpikingLayer(d_model)
        
        # Transformer layers
        self.layers = nn.ModuleList([
            SimplifiedTSNNLayer(d_model, n_heads, d_ff, dropout)
            for _ in range(num_layers)
        ])
        
        # Output head
        self.layer_norm = nn.LayerNorm(d_model)
        self.output_projection = nn.Linear(d_model, vocab_size)
        
        # Initialize weights
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        """Initialize model weights."""
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)
            
    def get_model_info(self) -> Dict[str, Any]:
        """Return model information."""
        return self.model_info.copy()
        
    def create_causal_mask(self, seq_len: int, device: torch.device) -> torch.Tensor:
        """Create causal attention mask."""
        mask = torch.tril(torch.ones(seq_len, seq_len, device=device))
        return mask
        
    def forward(self,
                input_ids: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        Forward pass of simplified TSNN model.
        
        Args:
            input_ids: Input token IDs [batch_size, seq_len]
            attention_mask: Attention mask [batch_size, seq_len]
            
        Returns:
            outputs: Dictionary containing logits and hidden states
        """
        batch_size, seq_len = input_ids.shape
        device = input_ids.device
        
        # Token and positional embeddings
        token_emb = self.token_embedding(input_ids)
        positions = torch.arange(seq_len, device=device)
        pos_emb = self.pos_embedding(positions)
        
        # Combine embeddings and apply spiking transformation
        embeddings = token_emb + pos_emb
        spike_embeddings = self.embedding_spikes(embeddings)
        
        # Create causal mask
        causal_mask = self.create_causal_mask(seq_len, device)
        
        # Combine with attention mask if provided
        if attention_mask is not None:
            # Create combined mask: causal AND attention
            # attention_mask: [batch_size, seq_len] -> [batch_size, seq_len, seq_len]
            expanded_attention = attention_mask.unsqueeze(1).expand(-1, seq_len, -1)  # [batch_size, seq_len, seq_len]
            expanded_attention = expanded_attention * attention_mask.unsqueeze(2)     # [batch_size, seq_len, seq_len]

            # Combine with causal mask: [seq_len, seq_len] -> [batch_size, seq_len, seq_len]
            causal_expanded = causal_mask.unsqueeze(0).expand(batch_size, -1, -1)    # [batch_size, seq_len, seq_len]
            combined_mask = causal_expanded * expanded_attention                      # [batch_size, seq_len, seq_len]
        else:
            combined_mask = causal_mask.unsqueeze(0).expand(batch_size, -1, -1)      # [batch_size, seq_len, seq_len]
        
        # Process through TSNN layers
        hidden_states = spike_embeddings
        for layer in self.layers:
            hidden_states = layer(hidden_states, combined_mask)
        
        # Final layer norm and projection
        hidden_states = self.layer_norm(hidden_states)
        logits = self.output_projection(hidden_states)
        
        return {
            "logits": logits,
            "last_hidden_state": hidden_states
        }
        
    def generate(self,
                 input_ids: torch.Tensor,
                 max_length: int = 50,
                 temperature: float = 1.0,
                 do_sample: bool = True,
                 top_k: int = 50,
                 top_p: float = 0.9,
                 pad_token_id: int = 0) -> torch.Tensor:
        """Generate text using the simplified TSNN model with repetition prevention."""
        self.eval()
        batch_size = input_ids.shape[0]
        device = input_ids.device

        generated = input_ids.clone()

        # Track recent tokens to prevent repetition
        recent_tokens = []
        repetition_window = 5

        with torch.no_grad():
            for step in range(max_length - input_ids.shape[1]):
                # Forward pass
                outputs = self.forward(generated)
                logits = outputs["logits"]

                # Get next token logits
                next_token_logits = logits[:, -1, :] / temperature

                # Apply repetition penalty
                if len(recent_tokens) > 0:
                    for token_id in set(recent_tokens):
                        next_token_logits[:, token_id] *= 0.8  # Reduce probability of recent tokens

                # Prevent immediate repetition of the last token
                if generated.shape[1] > 0:
                    last_token = generated[:, -1]
                    next_token_logits[:, last_token] *= 0.5

                # FORCE: Heavily penalize EOS token during generation (except at very end)
                if step < max_new_tokens - 2:  # Don't penalize at the very end
                    eos_token_id = 3  # Based on debug output
                    next_token_logits[:, eos_token_id] = -1e9  # Make EOS very unlikely

                if do_sample:
                    # Apply top-k filtering
                    if top_k > 0:
                        indices_to_remove = next_token_logits < torch.topk(
                            next_token_logits, top_k
                        )[0][..., -1, None]
                        next_token_logits[indices_to_remove] = -float('inf')

                    # Apply top-p (nucleus) filtering
                    if top_p < 1.0:
                        sorted_logits, sorted_indices = torch.sort(next_token_logits, descending=True)
                        cumulative_probs = torch.cumsum(F.softmax(sorted_logits, dim=-1), dim=-1)

                        # Remove tokens with cumulative probability above the threshold
                        sorted_indices_to_remove = cumulative_probs > top_p
                        sorted_indices_to_remove[..., 1:] = sorted_indices_to_remove[..., :-1].clone()
                        sorted_indices_to_remove[..., 0] = 0

                        indices_to_remove = sorted_indices_to_remove.scatter(1, sorted_indices, sorted_indices_to_remove)
                        next_token_logits[indices_to_remove] = -float('inf')

                    # Sample next token
                    probs = F.softmax(next_token_logits, dim=-1)
                    next_token = torch.multinomial(probs, num_samples=1)
                else:
                    # Greedy decoding
                    next_token = torch.argmax(next_token_logits, dim=-1, keepdim=True)

                # Update recent tokens tracking
                recent_tokens.append(next_token.item())
                if len(recent_tokens) > repetition_window:
                    recent_tokens.pop(0)

                # Append to generated sequence
                generated = torch.cat([generated, next_token], dim=1)

                # Stop if we generate an end token or pad token
                if next_token.item() == pad_token_id:
                    break

        return generated
