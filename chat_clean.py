#!/usr/bin/env python3
"""
Chat with the Clean-Trained TSNN Model
This should now generate coherent responses!
"""

import torch
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tsnn.simple_tsnn import SimplifiedTSNNModel
from tsnn.tokenizer import TSNNTokenizer


def load_clean_model():
    """Load the clean-trained TSNN model."""
    model_path = "models/tsnn_clean_final.pt"
    
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        return None, None
    
    print("🔄 Loading clean-trained TSNN model...")
    
    # Load checkpoint
    checkpoint = torch.load(model_path, map_location='cpu')
    config = checkpoint.get('config', {})
    
    print(f"📊 Model: {config.get('d_model', 'unknown')}d, {config.get('n_heads', 'unknown')}h, {config.get('num_layers', 'unknown')}l")
    
    # Initialize tokenizer and model
    tokenizer = TSNNTokenizer()
    
    model = SimplifiedTSNNModel(
        vocab_size=config.get('vocab_size', tokenizer.vocab_size),
        d_model=config.get('d_model', 512),
        n_heads=config.get('n_heads', 8),
        num_layers=config.get('num_layers', 8),
        d_ff=config.get('d_ff', 2048),
        max_seq_len=config.get('max_seq_len', 256),
        dropout=config.get('dropout', 0.1)
    )
    
    # Load weights
    try:
        model.load_state_dict(checkpoint['model_state_dict'])
        print("✅ Clean TSNN model loaded successfully!")
        print(f"🧠 Parameters: {sum(p.numel() for p in model.parameters()):,}")
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None, None
    
    model.eval()
    
    # Move to GPU if available
    device = "cuda" if torch.cuda.is_available() else "cpu"
    model = model.to(device)
    print(f"🎮 Device: {device}")
    
    return model, tokenizer


def generate_response(model, tokenizer, user_input: str):
    """Generate response using the clean TSNN model."""
    
    device = next(model.parameters()).device
    
    # Create conversation context
    context = f"User: {user_input}\nAI:"
    
    try:
        # Tokenize
        encoding = tokenizer.encode(context, max_length=200, padding=False, truncation=True)
        input_ids = encoding["input_ids"].unsqueeze(0).to(device)
        
        # Generate with good parameters
        with torch.no_grad():
            generated = model.generate(
                input_ids=input_ids,
                max_length=input_ids.shape[1] + 50,
                temperature=0.8,
                do_sample=True,
                top_k=40,
                top_p=0.9,
                pad_token_id=tokenizer.pad_token_id
            )
        
        # Extract generated part
        generated_part = generated[:, input_ids.shape[1]:]
        
        # Decode
        response = tokenizer.decode(generated_part[0], skip_special_tokens=True)
        
        # Clean response
        if "User:" in response:
            response = response.split("User:")[0].strip()
        if "AI:" in response:
            response = response.split("AI:")[-1].strip()
        
        # Remove extra whitespace
        response = " ".join(response.split())
        
        return response if response else "I'm ready to help you!"
        
    except Exception as e:
        print(f"❌ Generation error: {e}")
        return "I encountered an error while generating a response."


def main():
    """Main chat interface."""
    print("🧠 Clean TSNN Chat Interface")
    print("Trained on 220,679 high-quality conversations!")
    print("=" * 60)
    
    # Load model
    model, tokenizer = load_clean_model()
    if model is None:
        return
    
    print("\n🎉 Your clean-trained TSNN AI is ready!")
    print("🎯 This should now generate coherent, natural responses!")
    print("Type 'quit' to exit, 'test' for test questions\n")
    
    # Chat loop
    while True:
        try:
            user_input = input("You: ").strip()
            
            if not user_input:
                continue
                
            if user_input.lower() in ['quit', 'exit']:
                print("👋 Goodbye! Your TSNN architecture is working beautifully!")
                break
                
            if user_input.lower() == 'test':
                test_questions = [
                    "Hello, what are you?",
                    "Who created you?",
                    "How do you work?",
                    "Tell me about TSNN architecture",
                    "Are you energy efficient?",
                    "Can you help me with coding?",
                    "What makes you special?"
                ]
                
                print("🧪 Running test questions...")
                for question in test_questions:
                    print(f"\n👤 Test: {question}")
                    response = generate_response(model, tokenizer, question)
                    print(f"🤖 AI: {response}")
                continue
            
            # Generate response
            print("🧠 AI is thinking...", end="", flush=True)
            response = generate_response(model, tokenizer, user_input)
            print(f"\r🤖 AI: {response}\n")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye! Your TSNN architecture is amazing!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            continue


if __name__ == "__main__":
    main()
